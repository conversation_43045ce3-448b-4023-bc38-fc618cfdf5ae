[{"D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx": "1", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts": "2", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx": "3", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx": "4", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx": "5", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx": "6", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx": "7", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx": "8", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx": "9", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx": "10", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx": "11", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx": "12", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx": "13", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx": "14", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx": "15", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx": "16", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts": "17", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx": "18", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx": "19", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts": "20", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts": "21", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx": "22", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts": "23", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts": "24", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts": "25", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts": "26", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts": "27", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx": "28", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx": "29", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx": "30", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts": "31", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts": "32", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts": "33", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx": "34", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx": "35", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts": "36", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx": "37", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx": "38", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx": "39", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx": "40", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx": "41", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx": "42", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx": "43", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx": "44", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "45", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx": "46", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx": "47", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts": "48", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts": "49", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts": "50", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts": "51", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts": "52", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts": "53", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts": "54", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts": "55", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts": "56", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx": "57", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx": "58", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx": "59", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts": "60", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx": "61", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx": "62", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts": "63", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts": "64", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts": "65", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx": "66", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx": "67", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts": "68"}, {"size": 554, "mtime": 1752306944000, "results": "69", "hashOfConfig": "70"}, {"size": 419, "mtime": 1751128028000, "results": "71", "hashOfConfig": "70"}, {"size": 6159, "mtime": 1753061514947, "results": "72", "hashOfConfig": "70"}, {"size": 7830, "mtime": 1752857570939, "results": "73", "hashOfConfig": "70"}, {"size": 3769, "mtime": 1752721564000, "results": "74", "hashOfConfig": "70"}, {"size": 6136, "mtime": 1752857570952, "results": "75", "hashOfConfig": "70"}, {"size": 7447, "mtime": 1752271830000, "results": "76", "hashOfConfig": "70"}, {"size": 15866, "mtime": 1752865554796, "results": "77", "hashOfConfig": "70"}, {"size": 46509, "mtime": 1753066054579, "results": "78", "hashOfConfig": "70"}, {"size": 45894, "mtime": 1752867480062, "results": "79", "hashOfConfig": "70"}, {"size": 15127, "mtime": 1752863733791, "results": "80", "hashOfConfig": "70"}, {"size": 5757, "mtime": 1752391390000, "results": "81", "hashOfConfig": "70"}, {"size": 90663, "mtime": 1752882565811, "results": "82", "hashOfConfig": "70"}, {"size": 5269, "mtime": 1751481708000, "results": "83", "hashOfConfig": "70"}, {"size": 62476, "mtime": 1752106868000, "results": "84", "hashOfConfig": "70"}, {"size": 85392, "mtime": 1752886750228, "results": "85", "hashOfConfig": "70"}, {"size": 56, "mtime": 1751129202000, "results": "86", "hashOfConfig": "70"}, {"size": 1342, "mtime": 1751155290000, "results": "87", "hashOfConfig": "70"}, {"size": 1688, "mtime": 1751208620000, "results": "88", "hashOfConfig": "70"}, {"size": 103, "mtime": 1751140878000, "results": "89", "hashOfConfig": "70"}, {"size": 232, "mtime": 1751541102000, "results": "90", "hashOfConfig": "70"}, {"size": 3996, "mtime": 1751807708000, "results": "91", "hashOfConfig": "70"}, {"size": 8102, "mtime": 1752863733789, "results": "92", "hashOfConfig": "70"}, {"size": 10798, "mtime": 1752863733876, "results": "93", "hashOfConfig": "70"}, {"size": 17132, "mtime": 1752721564000, "results": "94", "hashOfConfig": "70"}, {"size": 6186, "mtime": 1751790574000, "results": "95", "hashOfConfig": "70"}, {"size": 14260, "mtime": 1753066054798, "results": "96", "hashOfConfig": "70"}, {"size": 8587, "mtime": 1752337674000, "results": "97", "hashOfConfig": "70"}, {"size": 23306, "mtime": 1752878817658, "results": "98", "hashOfConfig": "70"}, {"size": 12906, "mtime": 1752333634000, "results": "99", "hashOfConfig": "70"}, {"size": 5715, "mtime": 1752872506322, "results": "100", "hashOfConfig": "70"}, {"size": 8904, "mtime": 1753066054580, "results": "101", "hashOfConfig": "70"}, {"size": 8214, "mtime": 1752334762000, "results": "102", "hashOfConfig": "70"}, {"size": 12982, "mtime": 1752333550000, "results": "103", "hashOfConfig": "70"}, {"size": 16951, "mtime": 1752879455591, "results": "104", "hashOfConfig": "70"}, {"size": 15593, "mtime": 1752760674000, "results": "105", "hashOfConfig": "70"}, {"size": 12325, "mtime": 1752330204000, "results": "106", "hashOfConfig": "70"}, {"size": 27939, "mtime": 1752867316202, "results": "107", "hashOfConfig": "70"}, {"size": 24168, "mtime": 1752867418410, "results": "108", "hashOfConfig": "70"}, {"size": 27156, "mtime": 1753056913916, "results": "109", "hashOfConfig": "70"}, {"size": 6146, "mtime": 1753061514947, "results": "110", "hashOfConfig": "70"}, {"size": 9288, "mtime": 1752863733792, "results": "111", "hashOfConfig": "70"}, {"size": 11500, "mtime": 1753061514980, "results": "112", "hashOfConfig": "70"}, {"size": 5908, "mtime": 1752330316000, "results": "113", "hashOfConfig": "70"}, {"size": 2063, "mtime": 1751140856000, "results": "114", "hashOfConfig": "70"}, {"size": 2236, "mtime": 1751374982000, "results": "115", "hashOfConfig": "70"}, {"size": 4237, "mtime": 1751374890000, "results": "116", "hashOfConfig": "70"}, {"size": 230, "mtime": 1751371668000, "results": "117", "hashOfConfig": "70"}, {"size": 10813, "mtime": 1752761372000, "results": "118", "hashOfConfig": "70"}, {"size": 14506, "mtime": 1752879590759, "results": "119", "hashOfConfig": "70"}, {"size": 26448, "mtime": 1752380598000, "results": "120", "hashOfConfig": "70"}, {"size": 10147, "mtime": 1752334796000, "results": "121", "hashOfConfig": "70"}, {"size": 10510, "mtime": 1752310980000, "results": "122", "hashOfConfig": "70"}, {"size": 10877, "mtime": 1752092600000, "results": "123", "hashOfConfig": "70"}, {"size": 7318, "mtime": 1752381124000, "results": "124", "hashOfConfig": "70"}, {"size": 5263, "mtime": 1752867135753, "results": "125", "hashOfConfig": "70"}, {"size": 19520, "mtime": 1752338090000, "results": "126", "hashOfConfig": "70"}, {"size": 13444, "mtime": 1752870507174, "results": "127", "hashOfConfig": "70"}, {"size": 16870, "mtime": 1752338106000, "results": "128", "hashOfConfig": "70"}, {"size": 616, "mtime": 1752865556056, "results": "129", "hashOfConfig": "70"}, {"size": 15760, "mtime": 1752828048867, "results": "130", "hashOfConfig": "70"}, {"size": 9958, "mtime": 1751375132000, "results": "131", "hashOfConfig": "70"}, {"size": 42, "mtime": 1751129052000, "results": "132", "hashOfConfig": "70"}, {"size": 20869, "mtime": 1752885135797, "results": "133", "hashOfConfig": "70"}, {"size": 10034, "mtime": 1752860879212, "results": "134", "hashOfConfig": "70"}, {"size": 9297, "mtime": 1751476824000, "results": "135", "hashOfConfig": "70"}, {"size": 3321, "mtime": 1752876410067, "results": "136", "hashOfConfig": "70"}, {"size": 3686, "mtime": 1752890875441, "results": "137", "hashOfConfig": "70"}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ycd4oj", {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx", ["342"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx", ["343"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx", ["344", "345", "346", "347"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx", ["348", "349", "350"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx", ["351", "352", "353", "354", "355", "356"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx", ["357", "358"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx", ["359", "360", "361", "362", "363", "364", "365"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx", ["366"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts", ["367"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts", ["368"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx", ["369"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx", ["370"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts", ["371"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts", ["372"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx", ["373"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts", ["374", "375", "376", "377", "378"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx", ["379", "380", "381", "382"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx", ["383"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx", ["384", "385", "386", "387"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts", ["388", "389", "390", "391", "392"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts", ["393", "394", "395", "396", "397"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts", ["398", "399", "400", "401", "402", "403"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts", ["404", "405", "406"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts", ["407"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx", ["408", "409"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx", ["410"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx", ["411", "412", "413"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx", ["414", "415", "416"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts", ["417"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx", ["418", "419", "420"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts", [], [], {"ruleId": "421", "severity": 1, "message": "422", "line": 3, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 22}, {"ruleId": "421", "severity": 1, "message": "425", "line": 2, "column": 28, "nodeType": "423", "messageId": "424", "endLine": 2, "endColumn": 38}, {"ruleId": "421", "severity": 1, "message": "426", "line": 6, "column": 132, "nodeType": "423", "messageId": "424", "endLine": 6, "endColumn": 137}, {"ruleId": "427", "severity": 1, "message": "428", "line": 215, "column": 6, "nodeType": "429", "endLine": 215, "endColumn": 57, "suggestions": "430"}, {"ruleId": "427", "severity": 1, "message": "431", "line": 215, "column": 7, "nodeType": "432", "endLine": 215, "endColumn": 32}, {"ruleId": "427", "severity": 1, "message": "431", "line": 215, "column": 34, "nodeType": "432", "endLine": 215, "endColumn": 56}, {"ruleId": "421", "severity": 1, "message": "433", "line": 53, "column": 19, "nodeType": "423", "messageId": "424", "endLine": 53, "endColumn": 29}, {"ruleId": "421", "severity": 1, "message": "434", "line": 83, "column": 5, "nodeType": "423", "messageId": "424", "endLine": 83, "endColumn": 10}, {"ruleId": "427", "severity": 1, "message": "435", "line": 162, "column": 6, "nodeType": "429", "endLine": 162, "endColumn": 82, "suggestions": "436"}, {"ruleId": "421", "severity": 1, "message": "437", "line": 3, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 29}, {"ruleId": "421", "severity": 1, "message": "438", "line": 23, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 23, "endColumn": 7}, {"ruleId": "427", "severity": 1, "message": "439", "line": 101, "column": 6, "nodeType": "429", "endLine": 101, "endColumn": 17, "suggestions": "440"}, {"ruleId": "421", "severity": 1, "message": "441", "line": 473, "column": 31, "nodeType": "423", "messageId": "424", "endLine": 473, "endColumn": 45}, {"ruleId": "421", "severity": 1, "message": "442", "line": 536, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 536, "endColumn": 28}, {"ruleId": "421", "severity": 1, "message": "443", "line": 702, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 702, "endColumn": 24}, {"ruleId": "421", "severity": 1, "message": "444", "line": 2, "column": 57, "nodeType": "423", "messageId": "424", "endLine": 2, "endColumn": 73}, {"ruleId": "421", "severity": 1, "message": "445", "line": 17, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 17, "endColumn": 23}, {"ruleId": "421", "severity": 1, "message": "437", "line": 6, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 6, "endColumn": 29}, {"ruleId": "421", "severity": 1, "message": "446", "line": 6, "column": 31, "nodeType": "423", "messageId": "424", "endLine": 6, "endColumn": 46}, {"ruleId": "421", "severity": 1, "message": "447", "line": 11, "column": 15, "nodeType": "423", "messageId": "424", "endLine": 11, "endColumn": 27}, {"ruleId": "427", "severity": 1, "message": "439", "line": 100, "column": 6, "nodeType": "429", "endLine": 100, "endColumn": 17, "suggestions": "448"}, {"ruleId": "421", "severity": 1, "message": "441", "line": 391, "column": 31, "nodeType": "423", "messageId": "424", "endLine": 391, "endColumn": 45}, {"ruleId": "421", "severity": 1, "message": "449", "line": 491, "column": 14, "nodeType": "423", "messageId": "424", "endLine": 491, "endColumn": 34}, {"ruleId": "421", "severity": 1, "message": "443", "line": 721, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 721, "endColumn": 24}, {"ruleId": "427", "severity": 1, "message": "450", "line": 39, "column": 6, "nodeType": "429", "endLine": 39, "endColumn": 16, "suggestions": "451"}, {"ruleId": "421", "severity": 1, "message": "452", "line": 35, "column": 32, "nodeType": "423", "messageId": "424", "endLine": 35, "endColumn": 33}, {"ruleId": "421", "severity": 1, "message": "453", "line": 3, "column": 25, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 37}, {"ruleId": "427", "severity": 1, "message": "439", "line": 64, "column": 6, "nodeType": "429", "endLine": 64, "endColumn": 17, "suggestions": "454"}, {"ruleId": "421", "severity": 1, "message": "455", "line": 93, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 93, "endColumn": 19}, {"ruleId": "421", "severity": 1, "message": "456", "line": 7, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 7, "endColumn": 15}, {"ruleId": "427", "severity": 1, "message": "457", "line": 57, "column": 6, "nodeType": "429", "endLine": 57, "endColumn": 16, "suggestions": "458"}, {"ruleId": "421", "severity": 1, "message": "455", "line": 93, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 93, "endColumn": 19}, {"ruleId": "421", "severity": 1, "message": "459", "line": 2, "column": 31, "nodeType": "423", "messageId": "424", "endLine": 2, "endColumn": 55}, {"ruleId": "421", "severity": 1, "message": "460", "line": 8, "column": 15, "nodeType": "423", "messageId": "424", "endLine": 8, "endColumn": 26}, {"ruleId": "427", "severity": 1, "message": "461", "line": 121, "column": 6, "nodeType": "429", "endLine": 121, "endColumn": 105, "suggestions": "462"}, {"ruleId": "427", "severity": 1, "message": "463", "line": 121, "column": 7, "nodeType": "432", "endLine": 121, "endColumn": 30}, {"ruleId": "427", "severity": 1, "message": "464", "line": 324, "column": 6, "nodeType": "429", "endLine": 324, "endColumn": 48, "suggestions": "465"}, {"ruleId": "421", "severity": 1, "message": "466", "line": 3, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 3, "endColumn": 21}, {"ruleId": "427", "severity": 1, "message": "439", "line": 56, "column": 6, "nodeType": "429", "endLine": 56, "endColumn": 17, "suggestions": "467"}, {"ruleId": "421", "severity": 1, "message": "468", "line": 147, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 147, "endColumn": 21}, {"ruleId": "427", "severity": 1, "message": "469", "line": 180, "column": 6, "nodeType": "429", "endLine": 180, "endColumn": 23, "suggestions": "470"}, {"ruleId": "427", "severity": 1, "message": "471", "line": 182, "column": 6, "nodeType": "429", "endLine": 182, "endColumn": 113, "suggestions": "472"}, {"ruleId": "421", "severity": 1, "message": "473", "line": 2, "column": 34, "nodeType": "423", "messageId": "424", "endLine": 2, "endColumn": 37}, {"ruleId": "427", "severity": 1, "message": "439", "line": 65, "column": 6, "nodeType": "429", "endLine": 65, "endColumn": 17, "suggestions": "474"}, {"ruleId": "475", "severity": 1, "message": "476", "line": 248, "column": 11, "nodeType": "477", "endLine": 262, "endColumn": 13}, {"ruleId": "475", "severity": 1, "message": "476", "line": 332, "column": 19, "nodeType": "477", "endLine": 346, "endColumn": 21}, {"ruleId": "421", "severity": 1, "message": "478", "line": 2, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 2, "endColumn": 24}, {"ruleId": "421", "severity": 1, "message": "479", "line": 12, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 12, "endColumn": 17}, {"ruleId": "421", "severity": 1, "message": "480", "line": 13, "column": 3, "nodeType": "423", "messageId": "424", "endLine": 13, "endColumn": 28}, {"ruleId": "427", "severity": 1, "message": "481", "line": 140, "column": 6, "nodeType": "429", "endLine": 140, "endColumn": 57, "suggestions": "482"}, {"ruleId": "483", "severity": 1, "message": "484", "line": 426, "column": 44, "nodeType": "485", "messageId": "486", "endLine": 426, "endColumn": 98}, {"ruleId": "421", "severity": 1, "message": "487", "line": 1, "column": 22, "nodeType": "423", "messageId": "424", "endLine": 1, "endColumn": 37}, {"ruleId": "421", "severity": 1, "message": "488", "line": 1, "column": 39, "nodeType": "423", "messageId": "424", "endLine": 1, "endColumn": 56}, {"ruleId": "421", "severity": 1, "message": "489", "line": 2, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 2, "endColumn": 26}, {"ruleId": "490", "severity": 1, "message": "491", "line": 581, "column": 3, "nodeType": "492", "messageId": "493", "endLine": 583, "endColumn": 4}, {"ruleId": "490", "severity": 1, "message": "491", "line": 647, "column": 3, "nodeType": "492", "messageId": "493", "endLine": 649, "endColumn": 4}, {"ruleId": "427", "severity": 1, "message": "494", "line": 128, "column": 6, "nodeType": "429", "endLine": 128, "endColumn": 18, "suggestions": "495"}, {"ruleId": "427", "severity": 1, "message": "496", "line": 173, "column": 6, "nodeType": "429", "endLine": 173, "endColumn": 33, "suggestions": "497"}, {"ruleId": "427", "severity": 1, "message": "496", "line": 204, "column": 6, "nodeType": "429", "endLine": 204, "endColumn": 21, "suggestions": "498"}, {"ruleId": "427", "severity": 1, "message": "496", "line": 239, "column": 6, "nodeType": "429", "endLine": 239, "endColumn": 33, "suggestions": "499"}, {"ruleId": "427", "severity": 1, "message": "500", "line": 299, "column": 6, "nodeType": "429", "endLine": 299, "endColumn": 22, "suggestions": "501"}, {"ruleId": "427", "severity": 1, "message": "502", "line": 306, "column": 6, "nodeType": "429", "endLine": 306, "endColumn": 18, "suggestions": "503"}, {"ruleId": "483", "severity": 1, "message": "484", "line": 35, "column": 44, "nodeType": "485", "messageId": "486", "endLine": 35, "endColumn": 98}, {"ruleId": "483", "severity": 1, "message": "504", "line": 96, "column": 46, "nodeType": "485", "messageId": "486", "endLine": 96, "endColumn": 97}, {"ruleId": "505", "severity": 1, "message": "506", "line": 234, "column": 1, "nodeType": "507", "endLine": 248, "endColumn": 3}, {"ruleId": "421", "severity": 1, "message": "508", "line": 23, "column": 11, "nodeType": "423", "messageId": "424", "endLine": 23, "endColumn": 28}, {"ruleId": "427", "severity": 1, "message": "439", "line": 100, "column": 6, "nodeType": "429", "endLine": 100, "endColumn": 17, "suggestions": "509"}, {"ruleId": "427", "severity": 1, "message": "510", "line": 375, "column": 6, "nodeType": "429", "endLine": 375, "endColumn": 8, "suggestions": "511"}, {"ruleId": "512", "severity": 1, "message": "513", "line": 259, "column": 7, "nodeType": "477", "endLine": 273, "endColumn": 8}, {"ruleId": "427", "severity": 1, "message": "439", "line": 66, "column": 6, "nodeType": "429", "endLine": 66, "endColumn": 17, "suggestions": "514"}, {"ruleId": "421", "severity": 1, "message": "515", "line": 385, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 385, "endColumn": 24}, {"ruleId": "427", "severity": 1, "message": "516", "line": 404, "column": 6, "nodeType": "429", "endLine": 404, "endColumn": 8, "suggestions": "517"}, {"ruleId": "421", "severity": 1, "message": "518", "line": 2, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 2, "endColumn": 14}, {"ruleId": "421", "severity": 1, "message": "519", "line": 90, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 90, "endColumn": 33}, {"ruleId": "421", "severity": 1, "message": "520", "line": 94, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 94, "endColumn": 26}, {"ruleId": "421", "severity": 1, "message": "521", "line": 4, "column": 27, "nodeType": "423", "messageId": "424", "endLine": 4, "endColumn": 39}, {"ruleId": "421", "severity": 1, "message": "518", "line": 2, "column": 10, "nodeType": "423", "messageId": "424", "endLine": 2, "endColumn": 14}, {"ruleId": "421", "severity": 1, "message": "519", "line": 105, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 105, "endColumn": 33}, {"ruleId": "421", "severity": 1, "message": "522", "line": 110, "column": 9, "nodeType": "423", "messageId": "424", "endLine": 110, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'AuthProvider' is defined but never used.", "Identifier", "unusedVar", "'ToastProps' is defined but never used.", "'Heart' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", "ArrayExpression", ["523"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'setFilters' is assigned a value but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'updateFilters'. Either include them or remove the dependency array.", ["524"], "'announcementService' is defined but never used.", "'Edit' is defined but never used.", "React Hook useEffect has a missing dependency: 'imageUrl'. Either include it or remove the dependency array.", ["525"], "'notificationId' is assigned a value but never used.", "'fetchRecentStudents' is assigned a value but never used.", "'combinedContent' is assigned a value but never used.", "'StudentsResponse' is defined but never used.", "'totalStudents' is assigned a value but never used.", "'calendarService' is defined but never used.", "'Announcement' is defined but never used.", ["526"], "'refreshAnnouncements' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["527"], "'T' is defined but never used.", "'API_BASE_URL' is defined but never used.", ["528"], "'markAsRead' is assigned a value but never used.", "'EventFilters' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleDeepLinkHighlighting'. Either include it or remove the dependency array.", ["529"], "'adminAnnouncementService' is defined but never used.", "'Subcategory' is defined but never used.", "React Hook useCallback has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["530"], "React Hook useCallback has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["531"], "'getImageUrl' is defined but never used.", ["532"], "'imageLoaded' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'goToNext' and 'goToPrevious'. Either include them or remove the dependency array.", ["533"], "React Hook useEffect has missing dependencies: 'announcement' and 'refreshImages'. Either include them or remove the dependency array.", ["534"], "'Eye' is defined but never used.", ["535"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'commentService' is defined but never used.", "'CommentFilters' is defined but never used.", "'PaginatedCommentsResponse' is defined but never used.", "React Hook useCallback has an unnecessary dependency: 'calendarId'. Either exclude it or remove the dependency array.", ["536"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentComment'.", "ArrowFunctionExpression", "unsafeRefs", "'adminHttpClient' is defined but never used.", "'studentHttpClient' is defined but never used.", "'AdminAuthService' is defined but never used.", "@typescript-eslint/no-useless-constructor", "Useless constructor.", "MethodDefinition", "noUselessConstructor", "React Hook useCallback has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["537"], "React Hook useCallback has missing dependencies: 'onError' and 'onSuccess'. Either include them or remove the dependency array. If 'onSuccess' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["538"], ["539"], ["540"], "React Hook useCallback has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["541"], "React Hook useEffect has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["542"], "Function declared in a loop contains unsafe references to variable(s) 'rootComment'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'skipScheduledDate' is assigned a value but never used.", ["543"], "React Hook useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["544"], "jsx-a11y/role-supports-aria-props", "The attribute aria-required is not supported by the role button.", ["545"], "'setPrimaryImage' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["546"], "'Link' is defined but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'handleForceLogout' is assigned a value but never used.", "'ReactionType' is defined but never used.", "'isEmail' is assigned a value but never used.", {"desc": "547", "fix": "548"}, {"desc": "549", "fix": "550"}, {"desc": "551", "fix": "552"}, {"desc": "551", "fix": "553"}, {"desc": "554", "fix": "555"}, {"desc": "551", "fix": "556"}, {"desc": "557", "fix": "558"}, {"desc": "559", "fix": "560"}, {"desc": "561", "fix": "562"}, {"desc": "551", "fix": "563"}, {"desc": "564", "fix": "565"}, {"desc": "566", "fix": "567"}, {"desc": "551", "fix": "568"}, {"desc": "569", "fix": "570"}, {"desc": "571", "fix": "572"}, {"desc": "573", "fix": "574"}, {"desc": "575", "fix": "576"}, {"desc": "573", "fix": "577"}, {"desc": "578", "fix": "579"}, {"desc": "580", "fix": "581"}, {"desc": "551", "fix": "582"}, {"desc": "583", "fix": "584"}, {"desc": "551", "fix": "585"}, {"desc": "583", "fix": "586"}, "Update the dependencies array to be: [currentDate]", {"range": "587", "text": "588"}, "Update the dependencies array to be: [debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", {"range": "589", "text": "590"}, "Update the dependencies array to be: [imagePath, imageUrl]", {"range": "591", "text": "592"}, {"range": "593", "text": "592"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "594", "text": "595"}, {"range": "596", "text": "592"}, "Update the dependencies array to be: [handleDeepLinkHighlighting, location]", {"range": "597", "text": "598"}, "Update the dependencies array to be: [clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", {"range": "599", "text": "600"}, "Update the dependencies array to be: [useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", {"range": "601", "text": "602"}, {"range": "603", "text": "592"}, "Update the dependencies array to be: [goToNext, goToPrevious, isOpen, onClose]", {"range": "604", "text": "605"}, "Update the dependencies array to be: [announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", {"range": "606", "text": "607"}, {"range": "608", "text": "592"}, "Update the dependencies array to be: [getCurrentUserContext, announcementId]", {"range": "609", "text": "610"}, "Update the dependencies array to be: [calendarId, onError]", {"range": "611", "text": "612"}, "Update the dependencies array to be: [calendarId, onError, onSuccess, refreshImages]", {"range": "613", "text": "614"}, "Update the dependencies array to be: [onError, onSuccess, refreshImages]", {"range": "615", "text": "616"}, {"range": "617", "text": "614"}, "Update the dependencies array to be: [pendingDeletes, refreshImages]", {"range": "618", "text": "619"}, "Update the dependencies array to be: [calendarId, refreshImages]", {"range": "620", "text": "621"}, {"range": "622", "text": "592"}, "Update the dependencies array to be: [images]", {"range": "623", "text": "624"}, {"range": "625", "text": "592"}, {"range": "626", "text": "624"}, [7834, 7885], "[currentDate]", [4777, 4853], "[debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", [3031, 3042], "[imagePath, imageUrl]", [2967, 2978], [900, 910], "[duration, handleClose]", [1735, 1746], [2143, 2153], "[handleDeepLinkHighlighting, location]", [4408, 4507], "[clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", [11180, 11222], "[useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", [1595, 1606], [4614, 4631], "[goTo<PERSON><PERSON>t, goToPrevious, isOpen, onClose]", [6335, 6442], "[announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", [1909, 1920], [4906, 4957], "[getCurrentUserContext, announcementId]", [4049, 4061], "[calendarId, onError]", [5454, 5481], "[calendarId, onError, onSuccess, refreshImages]", [6495, 6510], "[onError, onSuccess, refreshImages]", [7671, 7698], [9871, 9887], "[pendingDeletes, refreshImages]", [10076, 10088], "[calendarId, refreshImages]", [2737, 2748], [10514, 10516], "[images]", [1825, 1836], [11634, 11636]]