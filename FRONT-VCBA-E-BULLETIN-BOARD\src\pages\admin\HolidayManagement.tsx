import React, { useState, useEffect } from 'react';
import { holidayService, type Holiday, type HolidaySyncStatus } from '../../services/holidayService';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import { Calendar, RefreshCw, Download, Trash2, Globe, MapPin, Settings, CheckCircle, XCircle } from 'lucide-react';

const HolidayManagement: React.FC = () => {
  const { isAuthenticated, user } = useAdminAuth();
  const [syncStatus, setSyncStatus] = useState<HolidaySyncStatus | null>(null);
  const [currentYearHolidays, setCurrentYearHolidays] = useState<Holiday[]>([]);
  const [nextYearHolidays, setNextYearHolidays] = useState<Holiday[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  useEffect(() => {
    if (isAuthenticated && user?.role === 'admin') {
      loadData();
    } else if (!isAuthenticated) {
      setMessage({ type: 'error', text: 'Please log in as an admin to access holiday management.' });
    }
  }, [isAuthenticated, user]);

  const loadData = async () => {
    setLoading(true);
    try {
      const [statusResponse, currentResponse, nextResponse] = await Promise.all([
        holidayService.getSyncStatus(),
        holidayService.getCurrentYearHolidays(),
        holidayService.getNextYearHolidays()
      ]);

      if (statusResponse.success && statusResponse.data) {
        setSyncStatus(statusResponse.data);
      } else if (!statusResponse.success && statusResponse.message?.includes('Authentication')) {
        setMessage({ type: 'error', text: 'Please ensure you are logged in as an admin to access holiday management.' });
        return;
      }

      if (currentResponse.success && currentResponse.data) {
        setCurrentYearHolidays(currentResponse.data.holidays);
      }

      if (nextResponse.success && nextResponse.data) {
        setNextYearHolidays(nextResponse.data.holidays);
      }

      // If we got here but have no data, show a helpful message
      if (!statusResponse.success || !currentResponse.success || !nextResponse.success) {
        setMessage({
          type: 'info',
          text: 'Some holiday data could not be loaded. Please check your authentication and try refreshing the page.'
        });
      }
    } catch (error) {
      console.error('Error loading holiday data:', error);
      setMessage({ type: 'error', text: 'Failed to load holiday data. Please ensure you are logged in as an admin.' });
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async (year: number, forceUpdate = false) => {
    setSyncing(true);
    try {
      const response = await holidayService.syncHolidays(year, forceUpdate);
      
      if (response.success && response.data) {
        const result = response.data;
        setMessage({
          type: 'success',
          text: `Holiday sync completed for ${year}: ${result.totalCreated} created, ${result.totalUpdated} updated`
        });
        await loadData(); // Reload data
      } else {
        setMessage({ type: 'error', text: response.message || 'Sync failed' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to sync holidays' });
    } finally {
      setSyncing(false);
    }
  };

  const handleDeleteYear = async (year: number) => {
    if (!confirm(`Are you sure you want to delete all auto-generated holidays for ${year}?`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await holidayService.deleteHolidaysByYear(year);
      
      if (response.success && response.data) {
        setMessage({
          type: 'success',
          text: `Deleted ${response.data.deletedCount} holidays for ${year}`
        });
        await loadData(); // Reload data
      } else {
        setMessage({ type: 'error', text: response.message || 'Delete failed' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to delete holidays' });
    } finally {
      setLoading(false);
    }
  };

  const testAPI = async () => {
    setLoading(true);
    try {
      const response = await holidayService.testAPI();
      
      if (response.success && response.data) {
        const data = response.data;
        setMessage({
          type: 'success',
          text: `API test successful: ${data.totalRelevantHolidays} relevant holidays found`
        });
      } else {
        setMessage({ type: 'error', text: 'API test failed' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'API test failed' });
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getHolidayIcon = (holiday: Holiday) => {
    return holiday.is_global ? <Globe size={16} className="text-red-600" /> : <MapPin size={16} className="text-blue-600" />;
  };

  const getHolidayTypeColor = (holiday: Holiday) => {
    return holiday.is_global ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800';
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '0.5rem' }}>
          Holiday Management
        </h1>
        <p style={{ color: '#6b7280' }}>
          Manage automatic holiday synchronization and view holiday calendars
        </p>
      </div>

      {/* Message */}
      {message && (
        <div style={{
          padding: '1rem',
          borderRadius: '8px',
          marginBottom: '1.5rem',
          backgroundColor: message.type === 'success' ? '#f0fdf4' : message.type === 'error' ? '#fef2f2' : '#f0f9ff',
          border: `1px solid ${message.type === 'success' ? '#bbf7d0' : message.type === 'error' ? '#fecaca' : '#bfdbfe'}`,
          color: message.type === 'success' ? '#166534' : message.type === 'error' ? '#dc2626' : '#1e40af'
        }}>
          {message.text}
        </div>
      )}

      {/* Sync Status Card */}
      {syncStatus && (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          marginBottom: '2rem'
        }}>
          <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '1rem', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <Settings size={20} />
            Sync Status
          </h2>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
            {/* Current Year */}
            <div style={{ padding: '1rem', backgroundColor: '#f9fafb', borderRadius: '8px' }}>
              <h3 style={{ fontWeight: '600', marginBottom: '0.5rem' }}>
                Current Year ({syncStatus.currentYear.year})
              </h3>
              <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                <p>Total holidays: {syncStatus.currentYear.total_holidays}</p>
                <p>International: {syncStatus.currentYear.international_count}</p>
                <p>Philippine: {syncStatus.currentYear.local_count}</p>
                <p>Last sync: {syncStatus.currentYear.last_sync ? formatDate(syncStatus.currentYear.last_sync) : 'Never'}</p>
              </div>
            </div>

            {/* Next Year */}
            <div style={{ padding: '1rem', backgroundColor: '#f9fafb', borderRadius: '8px' }}>
              <h3 style={{ fontWeight: '600', marginBottom: '0.5rem' }}>
                Next Year ({syncStatus.nextYear.year})
              </h3>
              <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                <p>Total holidays: {syncStatus.nextYear.total_holidays}</p>
                <p>International: {syncStatus.nextYear.international_count}</p>
                <p>Philippine: {syncStatus.nextYear.local_count}</p>
                <p>Last sync: {syncStatus.nextYear.last_sync ? formatDate(syncStatus.nextYear.last_sync) : 'Never'}</p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem', flexWrap: 'wrap' }}>
            <button
              onClick={() => handleSync(syncStatus.currentYear.year, false)}
              disabled={syncing || loading}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.75rem 1rem',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: syncing || loading ? 'not-allowed' : 'pointer',
                opacity: syncing || loading ? 0.6 : 1
              }}
            >
              <RefreshCw size={16} />
              Sync Current Year
            </button>

            <button
              onClick={() => handleSync(syncStatus.nextYear.year, false)}
              disabled={syncing || loading}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.75rem 1rem',
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: syncing || loading ? 'not-allowed' : 'pointer',
                opacity: syncing || loading ? 0.6 : 1
              }}
            >
              <Download size={16} />
              Sync Next Year
            </button>

            <button
              onClick={testAPI}
              disabled={loading}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.75rem 1rem',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.6 : 1
              }}
            >
              <CheckCircle size={16} />
              Test API
            </button>
          </div>
        </div>
      )}

      {/* Holiday Lists */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(500px, 1fr))', gap: '2rem' }}>
        {/* Current Year Holidays */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h2 style={{ fontSize: '1.25rem', fontWeight: '600', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <Calendar size={20} />
              {new Date().getFullYear()} Holidays ({currentYearHolidays.length})
            </h2>
            <button
              onClick={() => handleDeleteYear(new Date().getFullYear())}
              disabled={loading || currentYearHolidays.length === 0}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.5rem 0.75rem',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '0.875rem',
                cursor: loading || currentYearHolidays.length === 0 ? 'not-allowed' : 'pointer',
                opacity: loading || currentYearHolidays.length === 0 ? 0.6 : 1
              }}
            >
              <Trash2 size={14} />
              Delete All
            </button>
          </div>

          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {currentYearHolidays.length === 0 ? (
              <p style={{ color: '#6b7280', textAlign: 'center', padding: '2rem' }}>
                No holidays found. Click "Sync Current Year" to fetch holidays.
              </p>
            ) : (
              currentYearHolidays.map((holiday) => (
                <div
                  key={holiday.calendar_id}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0.75rem',
                    borderBottom: '1px solid #e5e7eb'
                  }}
                >
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.25rem' }}>
                      {getHolidayIcon(holiday)}
                      <span style={{ fontWeight: '500' }}>{holiday.title}</span>
                      <span style={{
                        fontSize: '0.75rem',
                        padding: '0.125rem 0.375rem',
                        borderRadius: '4px'
                      }} className={getHolidayTypeColor(holiday)}>
                        {holiday.is_global ? 'International' : 'Philippine'}
                      </span>
                    </div>
                    <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                      {formatDate(holiday.event_date)}
                    </div>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    {holiday.is_active ? (
                      <CheckCircle size={16} className="text-green-600" />
                    ) : (
                      <XCircle size={16} className="text-red-600" />
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Next Year Holidays */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <h2 style={{ fontSize: '1.25rem', fontWeight: '600', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <Calendar size={20} />
              {new Date().getFullYear() + 1} Holidays ({nextYearHolidays.length})
            </h2>
            <button
              onClick={() => handleDeleteYear(new Date().getFullYear() + 1)}
              disabled={loading || nextYearHolidays.length === 0}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.5rem 0.75rem',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '0.875rem',
                cursor: loading || nextYearHolidays.length === 0 ? 'not-allowed' : 'pointer',
                opacity: loading || nextYearHolidays.length === 0 ? 0.6 : 1
              }}
            >
              <Trash2 size={14} />
              Delete All
            </button>
          </div>

          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {nextYearHolidays.length === 0 ? (
              <p style={{ color: '#6b7280', textAlign: 'center', padding: '2rem' }}>
                No holidays found. Click "Sync Next Year" to fetch holidays.
              </p>
            ) : (
              nextYearHolidays.map((holiday) => (
                <div
                  key={holiday.calendar_id}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0.75rem',
                    borderBottom: '1px solid #e5e7eb'
                  }}
                >
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.25rem' }}>
                      {getHolidayIcon(holiday)}
                      <span style={{ fontWeight: '500' }}>{holiday.title}</span>
                      <span style={{
                        fontSize: '0.75rem',
                        padding: '0.125rem 0.375rem',
                        borderRadius: '4px'
                      }} className={getHolidayTypeColor(holiday)}>
                        {holiday.is_global ? 'International' : 'Philippine'}
                      </span>
                    </div>
                    <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                      {formatDate(holiday.event_date)}
                    </div>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    {holiday.is_active ? (
                      <CheckCircle size={16} className="text-green-600" />
                    ) : (
                      <XCircle size={16} className="text-red-600" />
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HolidayManagement;
