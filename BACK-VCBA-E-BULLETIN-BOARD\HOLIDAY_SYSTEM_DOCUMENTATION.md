# Holiday System Documentation

## Overview

The Holiday System automatically fetches and displays public holidays in your e-bulletin calendar system. It integrates with the Nager.Date API to fetch internationally recognized holidays and Philippine holidays, filtering them according to your specified criteria.

## Features

### ✅ **Implemented Features**

1. **Automatic Holiday Fetching**
   - Fetches holidays from Nager.Date API (free, no API key required)
   - Supports Philippine holidays and internationally recognized holidays
   - Filters holidays based on predefined criteria

2. **Smart Holiday Filtering**
   - **International Holidays**: New Year's Day, Valentine's Day, Easter Sunday, Mother's Day, Father's Day, Halloween, Christmas Day, Christmas Eve
   - **Philippine Holidays**: Rizal Day, Independence Day, National Heroes Day, Bonifacio Day, Labor Day, All Saints' Day, etc.
   - **Excludes**: Country-specific holidays from other countries

3. **Database Integration**
   - Stores holidays in existing `school_calendar` table
   - Uses proper categorization (General > Holidays)
   - Maintains data integrity with existing calendar system

4. **Visual Distinction**
   - 🌍 International holidays (red styling)
   - 🇵🇭 Philippine holidays (blue styling)
   - 🤖 Auto-generated indicator
   - Special holiday icons and colors

5. **Admin Management**
   - Manual holiday sync for any year
   - View sync status and statistics
   - Delete auto-generated holidays
   - Test API connectivity
   - Force update existing holidays

6. **Scheduled Sync (Optional)**
   - Automatic annual sync (disabled by default)
   - Monthly status checks
   - Configurable sync schedule

## Database Schema

The system uses your existing `school_calendar` table with these key fields:

```sql
-- Holiday-specific fields
is_holiday = 1                    -- Marks as holiday
holiday_type = 'international'|'local'  -- Holiday type
is_auto_generated = 1             -- Auto-generated flag
api_source = 'nager.date'         -- API source
is_global = 1|0                   -- International vs local
country_code = 'PH'|'US'          -- Country code
local_name = 'Local holiday name' -- Local language name

-- Standard calendar fields
category_id = 1                   -- General category
subcategory_id = 13               -- Holidays subcategory
is_recurring = 1                  -- Yearly recurrence
recurrence_pattern = 'yearly'     -- Recurrence pattern
```

## API Endpoints

### Public Endpoints
- `GET /api/holidays/config` - Get holiday configuration
- `GET /api/holidays/test-api` - Test API connectivity

### Protected Endpoints (Admin only)
- `POST /api/holidays/sync` - Sync holidays for a year
- `GET /api/holidays/sync-status` - Get sync status
- `GET /api/holidays/:year` - Get holidays for a year
- `DELETE /api/holidays/:year` - Delete auto-generated holidays

## Configuration

Add to your `.env` file:

```env
# Holiday Configuration
HOLIDAY_AUTO_SYNC=false           # Enable automatic sync
HOLIDAY_SYNC_TIME=0 2 1 1 *       # Cron schedule (Jan 1 at 2 AM)
HOLIDAY_API_SOURCE=nager.date     # API source
HOLIDAY_SYNC_YEARS_AHEAD=2        # Years to sync ahead
```

## Usage

### 1. Manual Holiday Sync

```javascript
// Backend
const holidayService = new HolidayService();
const results = await holidayService.syncHolidaysForYear(2025, false);

// Frontend
import { holidayService } from '../services/holidayService';
const response = await holidayService.syncHolidays(2025, false);
```

### 2. View Holidays in Calendar

Holidays automatically appear in your calendar with:
- Special styling (red for international, blue for Philippine)
- Holiday icons (🌍 for international, 🇵🇭 for Philippine)
- Auto-generated indicator (🤖)
- Read-only status (cannot be deleted by users)

### 3. Admin Management

Navigate to the Holiday Management page to:
- View sync status
- Manually sync holidays
- Test API connectivity
- Delete auto-generated holidays
- View holiday statistics

## Holiday Lists

### International Holidays (Globally Recognized)
- New Year's Day (January 1)
- Valentine's Day (February 14)
- Easter Sunday (moveable)
- Mother's Day (2nd Sunday of May)
- Father's Day (3rd Sunday of June)
- Halloween (October 31)
- Christmas Eve (December 24)
- Christmas Day (December 25)

### Philippine Holidays
- Rizal Day (December 30)
- Independence Day (June 12)
- National Heroes Day (last Monday of August)
- Bonifacio Day (November 30)
- People Power Anniversary (February 25)
- Araw ng Kagitingan (April 9)
- Labor Day (May 1)
- Ninoy Aquino Day (August 21)
- All Saints' Day (November 1)
- Immaculate Conception (December 8)

## Testing

Run the comprehensive test suite:

```bash
# Test API integration
node scripts/test-holiday-api.js

# Test holiday sync
node scripts/test-holiday-sync.js

# Test API endpoints
node scripts/test-holiday-endpoints.js

# Test complete system
node scripts/test-complete-holiday-system.js
```

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check internet connection
   - Verify Nager.Date API is accessible
   - Run `node scripts/test-holiday-api.js`

2. **No Holidays Synced**
   - Check database connection
   - Verify categories exist (General, Holidays)
   - Check holiday filtering criteria

3. **Holidays Not Displaying**
   - Verify `is_holiday = 1` in database
   - Check calendar view API response
   - Ensure frontend styling is applied

### Logs

Check logs for holiday-related activities:
- Holiday sync operations
- API requests and responses
- Database operations
- Scheduler activities

## Security

- All holiday management endpoints require admin authentication
- Public endpoints are read-only
- No sensitive data is exposed
- API requests are rate-limited by external service

## Performance

- Holidays are cached in local database
- API calls are minimized (only during sync)
- Database queries are optimized
- Sync operations are logged for monitoring

## Future Enhancements

Potential improvements:
- Custom holiday management (add/edit holidays)
- Multiple API source support
- Holiday notifications
- Holiday-based school closure automation
- Regional holiday customization

---

**Status**: ✅ **FULLY IMPLEMENTED AND TESTED**

The holiday system is ready for production use and will automatically display globally recognized and Philippine holidays in your e-bulletin calendar system.
