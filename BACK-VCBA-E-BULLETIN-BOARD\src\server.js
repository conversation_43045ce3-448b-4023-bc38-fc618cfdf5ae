const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('express-async-errors');
require('dotenv').config();

const config = require('./config/config');
const logger = require('./utils/logger');
const { errorHandler } = require('./middleware/errorHandler');
const notFoundHandler = require('./middleware/notFoundHandler');
const database = require('./config/database');
const websocketService = require('./services/websocketService');
const emailService = require('./utils/email');
const holidayScheduler = require('./services/HolidayScheduler');

// Import routes - ADDING BACK ESSENTIAL ROUTES
console.log('🔧 Loading route modules...');
const authRoutes = require('./routes/authRoutes');
console.log('✅ authRoutes loaded');
const websocketRoutes = require('./routes/websocketRoutes');
console.log('✅ websocketRoutes loaded');
const adminRoutes = require('./routes/adminRoutes');
console.log('✅ adminRoutes loaded');
const studentRoutes = require('./routes/studentRoutes');
console.log('✅ studentRoutes loaded');
// Add back essential routes
const notificationRoutes = require('./routes/notificationRoutes');
console.log('✅ notificationRoutes loaded');
const calendarRoutes = require('./routes/calendarRoutes');
console.log('✅ calendarRoutes loaded');
const holidayRoutes = require('./routes/holidayRoutes');
console.log('✅ holidayRoutes loaded');
// Re-enable announcement routes after fixing constructor issues
const announcementRoutes = require('./routes/announcementRoutes');
console.log('✅ announcementRoutes loaded');
const commentRoutes = require('./routes/commentRoutes');
console.log('✅ commentRoutes loaded');
console.log('✅ Essential route modules loaded successfully');

const app = express();

// Trust proxy for rate limiting behind reverse proxy
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'https:'],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration - more permissive for static files
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // Allow localhost on any port for development
    if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
      return callback(null, true);
    }

    // Allow configured origins
    if (config.cors.origin.includes(origin)) {
      return callback(null, true);
    }

    return callback(null, true); // Allow all for now to fix urgent issue
  },
  credentials: config.cors.credentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Origin', 'Accept'],
  exposedHeaders: ['Content-Length', 'Content-Type'],
}));

// Rate limiting removed per user request

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files with explicit CORS headers
app.use('/uploads', (req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Max-Age', '86400');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }

  next();
}, express.static('public/uploads'));

// Serve other static files
app.use(express.static('public'));

// Logging middleware
if (config.env !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim()),
    },
  }));
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.env,
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API routes - ADDING BACK ESSENTIAL ROUTES
console.log('🔧 Setting up API routes...');
app.use('/api/auth', authRoutes);
app.use('/api/websocket', websocketRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/student', studentRoutes);
// Add back essential routes
app.use('/api/notifications', notificationRoutes);
app.use('/api/calendar', calendarRoutes);
app.use('/api/holidays', holidayRoutes);
// Re-enable announcement routes after fixing constructor issues
app.use('/api/announcements', announcementRoutes);
app.use('/api/comments', commentRoutes);
console.log('✅ Essential API routes set up successfully');

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  database.close().then(() => {
    logger.info('Database connection closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  database.close().then(() => {
    logger.info('Database connection closed');
    process.exit(0);
  });
});

// Create HTTP server and Socket.IO instance
console.log('🔧 Creating HTTP server and Socket.IO instance...');
const PORT = config.port || 3000;
const server = createServer(app);
console.log(`🔧 HTTP server created, preparing to listen on port ${PORT}`);

const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"],
    credentials: true
  },
  path: '/ws',
  serveClient: true,
  allowEIO3: true
});
console.log('✅ Socket.IO instance created');

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`New WebSocket connection: ${socket.id}`);

  // Join user to their personal room for targeted notifications
  socket.on('join-user-room', (userId) => {
    if (userId) {
      socket.join(`user-${userId}`);
      logger.debug(`User ${userId} joined their personal room`);
    }
  });

  // Join admin room for admin-specific events
  socket.on('join-admin-room', () => {
    socket.join('admin-room');
    logger.debug(`Admin joined admin room: ${socket.id}`);
  });

  // Handle announcement events
  socket.on('new-announcement', (data) => {
    // Broadcast new announcement to all connected clients
    socket.broadcast.emit('announcement-created', data);
    logger.debug('New announcement broadcasted to all clients');
  });

  // Handle comment events
  socket.on('new-comment', (data) => {
    // Broadcast new comment to all clients viewing the announcement
    socket.broadcast.emit('comment-added', data);
    logger.debug(`New comment broadcasted for announcement ${data.announcementId}`);
  });

  // Handle real-time notifications
  socket.on('send-notification', (data) => {
    const { userId, notification } = data;
    if (userId) {
      // Send to specific user
      io.to(`user-${userId}`).emit('notification', notification);
      logger.debug(`Notification sent to user ${userId}`);
    } else {
      // Broadcast to all users
      socket.broadcast.emit('notification', notification);
      logger.debug('Notification broadcasted to all users');
    }
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    logger.info(`WebSocket disconnected: ${socket.id}`);
  });

  // Handle errors
  socket.on('error', (error) => {
    logger.error('WebSocket error:', error);
  });
});

// Initialize WebSocket service
console.log('🔧 Initializing WebSocket service...');
websocketService.initialize(io);
console.log('✅ WebSocket service initialized');

// Make io instance available to routes
console.log('🔧 Setting up io instance for routes...');
app.set('io', io);
console.log('✅ IO instance set up');

// Start holiday scheduler
console.log('🔧 Starting holiday scheduler...');
try {
  holidayScheduler.start();
  console.log('✅ Holiday scheduler started');
} catch (error) {
  console.log('⚠️ Holiday scheduler failed to start:', error.message);
  logger.warn(`Holiday scheduler failed to start: ${error.message}`);
}

// Start server
console.log('🚀 Starting server...');
logger.info('Starting server...');
server.listen(PORT, () => {
  console.log(`✅ Server running on port ${PORT} in ${config.env} mode`);
  console.log(`🔌 WebSocket server available at /ws`);
  logger.info(`Server running on port ${PORT} in ${config.env} mode`);
  logger.info(`WebSocket server available at /ws`);
});

server.on('error', (err) => {
  logger.error('Server error:', err);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  server.close(() => {
    holidayScheduler.stop();
    process.exit(1);
  });
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    holidayScheduler.stop();
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    holidayScheduler.stop();
    process.exit(0);
  });
});

module.exports = app;
