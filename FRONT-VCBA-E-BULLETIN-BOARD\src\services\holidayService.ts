import { httpClient } from './api.service';
import type { ApiResponse } from '../types';

// Holiday types
export interface Holiday {
  calendar_id: number;
  title: string;
  description: string;
  event_date: string;
  end_date: string;
  is_holiday: boolean;
  holiday_type: 'local' | 'international' | 'school';
  country_code: string;
  is_auto_generated: boolean;
  api_source: string;
  local_name: string;
  is_global: boolean;
  is_fixed: boolean;
  is_active: boolean;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

export interface HolidaySyncResult {
  year: number;
  totalFetched: number;
  totalFiltered: number;
  totalCreated: number;
  totalUpdated: number;
  totalSkipped: number;
  errors: string[];
  holidays: Array<{
    name: string;
    date: string;
    action: 'created' | 'updated' | 'skipped';
    type: 'international' | 'philippine';
  }>;
}

export interface HolidaySyncStatus {
  currentYear: {
    year: number;
    total_holidays: number;
    international_count: number;
    local_count: number;
    last_sync: string | null;
  };
  nextYear: {
    year: number;
    total_holidays: number;
    international_count: number;
    local_count: number;
    last_sync: string | null;
  };
  apiStatus: string;
}

export interface HolidayConfig {
  supportedCountries: Array<{
    code: string;
    name: string;
    type: string;
  }>;
  globalHolidays: string[];
  philippineHolidays: string[];
  apiSource: string;
  syncSettings: {
    autoSync: boolean;
    syncYearsAhead: number;
    lastSync: string | null;
  };
}

/**
 * Holiday Service for managing public holidays
 */
class HolidayService {
  private baseUrl = '/api/holidays';

  /**
   * Sync holidays for a specific year
   */
  async syncHolidays(year: number, forceUpdate = false): Promise<ApiResponse<HolidaySyncResult>> {
    return httpClient.post(`${this.baseUrl}/sync`, {
      year,
      force_update: forceUpdate
    });
  }

  /**
   * Get holiday sync status
   */
  async getSyncStatus(): Promise<ApiResponse<HolidaySyncStatus>> {
    return httpClient.get(`${this.baseUrl}/sync-status`);
  }

  /**
   * Get holidays for a specific year
   */
  async getHolidaysByYear(year: number): Promise<ApiResponse<{ year: number; holidays: Holiday[]; total: number }>> {
    return httpClient.get(`${this.baseUrl}/${year}`);
  }

  /**
   * Delete auto-generated holidays for a specific year
   */
  async deleteHolidaysByYear(year: number): Promise<ApiResponse<{ year: number; deletedCount: number }>> {
    return httpClient.delete(`${this.baseUrl}/${year}`);
  }

  /**
   * Test holiday API connectivity
   */
  async testAPI(): Promise<ApiResponse<{
    apiStatus: string;
    year: number;
    philippineHolidays: { total: number; filtered: number };
    internationalHolidays: { total: number; filtered: number };
    totalRelevantHolidays: number;
  }>> {
    return httpClient.getPublic(`${this.baseUrl}/test-api`);
  }

  /**
   * Get holiday configuration
   */
  async getConfig(): Promise<ApiResponse<HolidayConfig>> {
    return httpClient.getPublic(`${this.baseUrl}/config`);
  }

  /**
   * Get current year holidays
   */
  async getCurrentYearHolidays(): Promise<ApiResponse<{ year: number; holidays: Holiday[]; total: number }>> {
    const currentYear = new Date().getFullYear();
    return this.getHolidaysByYear(currentYear);
  }

  /**
   * Get next year holidays
   */
  async getNextYearHolidays(): Promise<ApiResponse<{ year: number; holidays: Holiday[]; total: number }>> {
    const nextYear = new Date().getFullYear() + 1;
    return this.getHolidaysByYear(nextYear);
  }

  /**
   * Sync current and next year holidays
   */
  async syncCurrentAndNextYear(forceUpdate = false): Promise<{
    currentYear: ApiResponse<HolidaySyncResult>;
    nextYear: ApiResponse<HolidaySyncResult>;
  }> {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;

    const [currentYearResult, nextYearResult] = await Promise.all([
      this.syncHolidays(currentYear, forceUpdate),
      this.syncHolidays(nextYear, forceUpdate)
    ]);

    return {
      currentYear: currentYearResult,
      nextYear: nextYearResult
    };
  }

  /**
   * Check if holiday sync is needed
   */
  async isSyncNeeded(): Promise<boolean> {
    try {
      const status = await this.getSyncStatus();
      if (!status.success || !status.data) return true;

      const currentYear = new Date().getFullYear();
      const currentYearData = status.data.currentYear;

      // Check if current year has no holidays
      if (currentYearData.total_holidays === 0) {
        return true;
      }

      // Check if last sync was more than 6 months ago
      if (currentYearData.last_sync) {
        const lastSync = new Date(currentYearData.last_sync);
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        if (lastSync < sixMonthsAgo) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Failed to check sync status:', error);
      return true; // Assume sync is needed if we can't check
    }
  }
}

export const holidayService = new HolidayService();
export default holidayService;
