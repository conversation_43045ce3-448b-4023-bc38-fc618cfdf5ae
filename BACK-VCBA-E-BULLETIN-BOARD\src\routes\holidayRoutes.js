const express = require('express');
const { body, param, query } = require('express-validator');
const { authenticate, authorize } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const HolidayController = require('../controllers/HolidayController');

const router = express.Router();

// Validation rules
const yearValidation = [
  param('year')
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Year must be a valid integer between 2020 and 2030')
];

const syncValidation = [
  body('year')
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Year must be a valid integer between 2020 and 2030'),
  body('force_update')
    .optional()
    .isBoolean()
    .withMessage('force_update must be a boolean')
];

// Public routes (with optional authentication for better logging)
router.get('/config', HolidayController.getConfig);
router.get('/test-api', HolidayController.testAPI);

// Protected routes (require admin authentication)
router.use(authenticate);
router.use(authorize(['admin'])); // Only admins can manage holidays

// Holiday management routes
router.post('/sync', syncValidation, validateRequest, HolidayController.syncHolidays);
router.get('/sync-status', HolidayController.getSyncStatus);
router.get('/:year', yearValidation, validateRequest, HolidayController.getHolidaysByYear);
router.delete('/:year', yearValidation, validateRequest, HolidayController.deleteHolidaysByYear);

module.exports = router;
