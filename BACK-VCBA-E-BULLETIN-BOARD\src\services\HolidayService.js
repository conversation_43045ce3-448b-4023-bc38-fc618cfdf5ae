const axios = require('axios');
const calendarModel = require('../models/CalendarModel');
const logger = require('../utils/logger');
const { DatabaseError } = require('../middleware/errorHandler');

/**
 * Holiday Service for fetching and managing public holidays
 * Integrates with external APIs and stores holidays in the school_calendar table
 */
class HolidayService {
  constructor() {
    this.calendarModel = calendarModel;
    this.nagerDateBaseUrl = 'https://date.nager.at/api/v3';
    
    // Define globally recognized holidays that we want to include
    this.globalHolidays = [
      'New Year\'s Day',
      'Valentine\'s Day', 
      'Easter Sunday',
      'Mother\'s Day',
      'Father\'s Day',
      'Halloween',
      'Christmas Day',
      'Christmas Eve'
    ];
    
    // Philippine holidays we want to include
    this.philippineHolidays = [
      'Rizal Day',
      'Independence Day',
      'National Heroes Day',
      'Bonifacio Day',
      'People Power Anniversary',
      'Araw ng Kagitingan',
      'Labor Day',
      'Ninoy Aquino Day',
      'All Saints\' Day',
      'Immaculate Conception',
      'Jose Rizal Day'
    ];
  }

  /**
   * Fetch holidays from Nager.Date API for a specific country and year
   * @param {string} countryCode - ISO 3166-1 alpha-2 country code
   * @param {number} year - Year to fetch holidays for
   * @returns {Promise<Array>} Array of holiday objects
   */
  async fetchHolidaysFromAPI(countryCode, year) {
    try {
      logger.info(`Fetching holidays for ${countryCode} ${year} from Nager.Date API`);
      
      const response = await axios.get(`${this.nagerDateBaseUrl}/PublicHolidays/${year}/${countryCode}`, {
        timeout: 10000,
        headers: {
          'User-Agent': 'VCBA-E-Bulletin-Board/1.0'
        }
      });

      if (response.status !== 200) {
        throw new Error(`API returned status ${response.status}`);
      }

      logger.info(`Successfully fetched ${response.data.length} holidays for ${countryCode} ${year}`);
      return response.data;
      
    } catch (error) {
      logger.error(`Failed to fetch holidays from API: ${error.message}`);
      
      if (error.code === 'ECONNABORTED') {
        throw new Error('Holiday API request timed out');
      } else if (error.response) {
        throw new Error(`Holiday API error: ${error.response.status} - ${error.response.statusText}`);
      } else if (error.request) {
        throw new Error('Holiday API is unreachable');
      } else {
        throw new Error(`Holiday API error: ${error.message}`);
      }
    }
  }

  /**
   * Check if a holiday name matches our criteria for global or Philippine holidays
   * @param {string} holidayName - Name of the holiday
   * @param {string} countryCode - Country code
   * @returns {Object} Object with isIncluded, isGlobal, and holidayType
   */
  isHolidayIncluded(holidayName, countryCode) {
    const normalizedName = holidayName.toLowerCase();
    
    // Check for global holidays (case-insensitive partial matching)
    const isGlobalHoliday = this.globalHolidays.some(globalHoliday => {
      const normalizedGlobal = globalHoliday.toLowerCase();
      return normalizedName.includes(normalizedGlobal) || 
             normalizedGlobal.includes(normalizedName) ||
             this.isHolidayNameSimilar(normalizedName, normalizedGlobal);
    });

    if (isGlobalHoliday) {
      return {
        isIncluded: true,
        isGlobal: true,
        holidayType: 'international'
      };
    }

    // Check for Philippine holidays if country is Philippines
    if (countryCode === 'PH') {
      const isPhilippineHoliday = this.philippineHolidays.some(phHoliday => {
        const normalizedPh = phHoliday.toLowerCase();
        return normalizedName.includes(normalizedPh) || 
               normalizedPh.includes(normalizedName) ||
               this.isHolidayNameSimilar(normalizedName, normalizedPh);
      });

      if (isPhilippineHoliday) {
        return {
          isIncluded: true,
          isGlobal: false,
          holidayType: 'local'
        };
      }
    }

    return {
      isIncluded: false,
      isGlobal: false,
      holidayType: null
    };
  }

  /**
   * Check if two holiday names are similar (handles variations in naming)
   * @param {string} name1 - First holiday name
   * @param {string} name2 - Second holiday name  
   * @returns {boolean} True if names are similar
   */
  isHolidayNameSimilar(name1, name2) {
    // Remove common words and check for similarity
    const commonWords = ['day', 'of', 'the', 'saint', 'st.', 'dr.', 'national'];
    
    const clean1 = name1.split(' ').filter(word => !commonWords.includes(word)).join(' ');
    const clean2 = name2.split(' ').filter(word => !commonWords.includes(word)).join(' ');
    
    return clean1.includes(clean2) || clean2.includes(clean1);
  }

  /**
   * Transform API holiday data to our database format
   * @param {Object} apiHoliday - Holiday data from API
   * @param {string} countryCode - Country code
   * @param {Object} inclusionInfo - Result from isHolidayIncluded
   * @returns {Object} Holiday data formatted for database
   */
  transformHolidayData(apiHoliday, countryCode, inclusionInfo) {
    return {
      title: apiHoliday.name,
      description: `${inclusionInfo.isGlobal ? 'International' : 'Philippine'} holiday - ${apiHoliday.name}`,
      event_date: apiHoliday.date,
      end_date: apiHoliday.date, // Most holidays are single day
      category_id: 1, // General category
      subcategory_id: 13, // Holidays subcategory
      is_recurring: 1, // Holidays typically recur yearly
      recurrence_pattern: 'yearly',
      is_active: 1,
      is_published: 1,
      allow_comments: 1,
      is_alert: 0,
      is_holiday: 1,
      holiday_type: inclusionInfo.holidayType,
      country_code: countryCode,
      is_auto_generated: 1,
      api_source: 'nager.date',
      local_name: apiHoliday.localName || apiHoliday.name,
      holiday_types: JSON.stringify(apiHoliday.types || []),
      is_global: inclusionInfo.isGlobal ? 1 : 0,
      is_fixed: apiHoliday.fixed ? 1 : 0,
      created_by: 1 // System user
    };
  }

  /**
   * Sync holidays for a specific year
   * @param {number} year - Year to sync holidays for
   * @param {boolean} forceUpdate - Whether to update existing holidays
   * @returns {Promise<Object>} Sync results
   */
  async syncHolidaysForYear(year, forceUpdate = false) {
    try {
      logger.info(`Starting holiday sync for year ${year}`);

      const results = {
        year,
        totalFetched: 0,
        totalFiltered: 0,
        totalCreated: 0,
        totalUpdated: 0,
        totalSkipped: 0,
        errors: [],
        holidays: []
      };

      // Fetch international holidays (using US as proxy for international holidays)
      const internationalHolidays = await this.fetchHolidaysFromAPI('US', year);
      results.totalFetched += internationalHolidays.length;

      // Fetch Philippine holidays
      const philippineHolidays = await this.fetchHolidaysFromAPI('PH', year);
      results.totalFetched += philippineHolidays.length;

      // Process international holidays
      for (const holiday of internationalHolidays) {
        const inclusionInfo = this.isHolidayIncluded(holiday.name, 'US');
        if (inclusionInfo.isIncluded && inclusionInfo.isGlobal) {
          results.totalFiltered++;
          const holidayData = this.transformHolidayData(holiday, 'US', inclusionInfo);

          try {
            const syncResult = await this.syncSingleHoliday(holidayData, forceUpdate);
            if (syncResult.action === 'created') results.totalCreated++;
            else if (syncResult.action === 'updated') results.totalUpdated++;
            else results.totalSkipped++;

            results.holidays.push({
              name: holiday.name,
              date: holiday.date,
              action: syncResult.action,
              type: 'international'
            });
          } catch (error) {
            results.errors.push(`Failed to sync ${holiday.name}: ${error.message}`);
          }
        }
      }

      // Process Philippine holidays
      for (const holiday of philippineHolidays) {
        const inclusionInfo = this.isHolidayIncluded(holiday.name, 'PH');
        if (inclusionInfo.isIncluded) {
          results.totalFiltered++;
          const holidayData = this.transformHolidayData(holiday, 'PH', inclusionInfo);

          try {
            const syncResult = await this.syncSingleHoliday(holidayData, forceUpdate);
            if (syncResult.action === 'created') results.totalCreated++;
            else if (syncResult.action === 'updated') results.totalUpdated++;
            else results.totalSkipped++;

            results.holidays.push({
              name: holiday.name,
              date: holiday.date,
              action: syncResult.action,
              type: 'philippine'
            });
          } catch (error) {
            results.errors.push(`Failed to sync ${holiday.name}: ${error.message}`);
          }
        }
      }

      logger.info(`Holiday sync completed for ${year}: ${results.totalCreated} created, ${results.totalUpdated} updated, ${results.totalSkipped} skipped`);
      return results;

    } catch (error) {
      logger.error(`Holiday sync failed for year ${year}: ${error.message}`);
      throw new DatabaseError(`Failed to sync holidays for year ${year}: ${error.message}`);
    }
  }

  /**
   * Sync a single holiday to the database
   * @param {Object} holidayData - Holiday data to sync
   * @param {boolean} forceUpdate - Whether to update if exists
   * @returns {Promise<Object>} Sync result
   */
  async syncSingleHoliday(holidayData, forceUpdate = false) {
    try {
      // Check if holiday already exists for this date and type
      const existingHoliday = await this.calendarModel.query(
        `SELECT calendar_id, title, is_auto_generated, api_source
         FROM school_calendar
         WHERE event_date = ? AND is_holiday = 1 AND is_auto_generated = 1
         AND (title = ? OR local_name = ?)
         AND deleted_at IS NULL`,
        [holidayData.event_date, holidayData.title, holidayData.local_name]
      );

      if (existingHoliday.length > 0) {
        if (forceUpdate) {
          // Update existing holiday
          const updated = await this.calendarModel.updateById(existingHoliday[0].calendar_id, {
            ...holidayData,
            updated_at: new Date()
          });
          return { action: 'updated', holiday: updated };
        } else {
          return { action: 'skipped', holiday: existingHoliday[0] };
        }
      } else {
        // Create new holiday
        const created = await this.calendarModel.create(holidayData);
        return { action: 'created', holiday: created };
      }

    } catch (error) {
      logger.error(`Failed to sync single holiday: ${error.message}`);
      throw new DatabaseError(`Failed to sync holiday: ${error.message}`);
    }
  }

  /**
   * Get all auto-generated holidays for a specific year
   * @param {number} year - Year to get holidays for
   * @returns {Promise<Array>} Array of holidays
   */
  async getAutoGeneratedHolidays(year) {
    try {
      const holidays = await this.calendarModel.query(
        `SELECT sc.*, c.name as category_name, s.name as subcategory_name
         FROM school_calendar sc
         LEFT JOIN categories c ON sc.category_id = c.category_id
         LEFT JOIN subcategories s ON sc.subcategory_id = s.subcategory_id
         WHERE YEAR(sc.event_date) = ?
         AND sc.is_holiday = 1
         AND sc.is_auto_generated = 1
         AND sc.deleted_at IS NULL
         ORDER BY sc.event_date ASC`,
        [year]
      );

      return holidays.map(holiday => this.calendarModel.formatEventDates(holiday));
    } catch (error) {
      logger.error(`Failed to get auto-generated holidays: ${error.message}`);
      throw new DatabaseError(`Failed to get holidays for year ${year}: ${error.message}`);
    }
  }

  /**
   * Delete all auto-generated holidays for a specific year
   * @param {number} year - Year to delete holidays for
   * @returns {Promise<number>} Number of deleted holidays
   */
  async deleteAutoGeneratedHolidays(year) {
    try {
      const result = await this.calendarModel.query(
        `UPDATE school_calendar
         SET deleted_at = NOW()
         WHERE YEAR(event_date) = ?
         AND is_holiday = 1
         AND is_auto_generated = 1
         AND deleted_at IS NULL`,
        [year]
      );

      logger.info(`Deleted ${result.affectedRows} auto-generated holidays for year ${year}`);
      return result.affectedRows;
    } catch (error) {
      logger.error(`Failed to delete auto-generated holidays: ${error.message}`);
      throw new DatabaseError(`Failed to delete holidays for year ${year}: ${error.message}`);
    }
  }

  /**
   * Get holiday sync status and statistics
   * @returns {Promise<Object>} Sync status information
   */
  async getHolidaySyncStatus() {
    try {
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      const [currentYearStats] = await this.calendarModel.query(
        `SELECT
           COUNT(*) as total_holidays,
           COUNT(CASE WHEN holiday_type = 'international' THEN 1 END) as international_count,
           COUNT(CASE WHEN holiday_type = 'local' THEN 1 END) as local_count,
           MAX(updated_at) as last_sync
         FROM school_calendar
         WHERE YEAR(event_date) = ?
         AND is_holiday = 1
         AND is_auto_generated = 1
         AND deleted_at IS NULL`,
        [currentYear]
      );

      const [nextYearStats] = await this.calendarModel.query(
        `SELECT
           COUNT(*) as total_holidays,
           COUNT(CASE WHEN holiday_type = 'international' THEN 1 END) as international_count,
           COUNT(CASE WHEN holiday_type = 'local' THEN 1 END) as local_count,
           MAX(updated_at) as last_sync
         FROM school_calendar
         WHERE YEAR(event_date) = ?
         AND is_holiday = 1
         AND is_auto_generated = 1
         AND deleted_at IS NULL`,
        [nextYear]
      );

      return {
        currentYear: {
          year: currentYear,
          ...currentYearStats
        },
        nextYear: {
          year: nextYear,
          ...nextYearStats
        },
        apiStatus: 'operational' // We'll implement API health check later
      };

    } catch (error) {
      logger.error(`Failed to get holiday sync status: ${error.message}`);
      throw new DatabaseError(`Failed to get sync status: ${error.message}`);
    }
  }
}

module.exports = HolidayService;
