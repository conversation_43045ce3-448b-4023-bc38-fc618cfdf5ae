const HolidayService = require('../src/services/HolidayService');

async function testHolidayAPI() {
  try {
    console.log('🧪 Testing Holiday API Integration...\n');
    
    const holidayService = new HolidayService();
    const currentYear = new Date().getFullYear();
    
    console.log('📅 Testing API fetch for Philippines...');
    const phHolidays = await holidayService.fetchHolidaysFromAPI('PH', currentYear);
    console.log(`✅ Fetched ${phHolidays.length} Philippine holidays for ${currentYear}`);
    
    console.log('\n📅 Testing API fetch for US (international holidays)...');
    const usHolidays = await holidayService.fetchHolidaysFromAPI('US', currentYear);
    console.log(`✅ Fetched ${usHolidays.length} US holidays for ${currentYear}`);
    
    console.log('\n🔍 Testing holiday filtering...');
    
    // Test Philippine holidays
    console.log('\nPhilippine holidays that match our criteria:');
    let phFiltered = 0;
    phHolidays.forEach(holiday => {
      const inclusion = holidayService.isHolidayIncluded(holiday.name, 'PH');
      if (inclusion.isIncluded) {
        console.log(`  ✅ ${holiday.name} (${holiday.date}) - ${inclusion.holidayType}`);
        phFiltered++;
      }
    });
    console.log(`Filtered: ${phFiltered}/${phHolidays.length} Philippine holidays`);
    
    // Test international holidays
    console.log('\nInternational holidays that match our criteria:');
    let intFiltered = 0;
    usHolidays.forEach(holiday => {
      const inclusion = holidayService.isHolidayIncluded(holiday.name, 'US');
      if (inclusion.isIncluded && inclusion.isGlobal) {
        console.log(`  ✅ ${holiday.name} (${holiday.date}) - ${inclusion.holidayType}`);
        intFiltered++;
      }
    });
    console.log(`Filtered: ${intFiltered}/${usHolidays.length} international holidays`);
    
    console.log('\n🔄 Testing data transformation...');
    
    // Test transforming a sample holiday
    const sampleHoliday = phHolidays.find(h => h.name.toLowerCase().includes('christmas')) || phHolidays[0];
    if (sampleHoliday) {
      const inclusion = holidayService.isHolidayIncluded(sampleHoliday.name, 'PH');
      if (inclusion.isIncluded) {
        const transformed = holidayService.transformHolidayData(sampleHoliday, 'PH', inclusion);
        console.log('Sample transformed holiday:');
        console.log(JSON.stringify(transformed, null, 2));
      }
    }
    
    console.log('\n✅ Holiday API integration test completed successfully!');
    console.log(`\nSummary:`);
    console.log(`- Philippine holidays: ${phFiltered} relevant out of ${phHolidays.length} total`);
    console.log(`- International holidays: ${intFiltered} relevant out of ${usHolidays.length} total`);
    console.log(`- Total holidays to sync: ${phFiltered + intFiltered}`);
    
  } catch (error) {
    console.error('❌ Holiday API test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testHolidayAPI()
    .then(() => {
      console.log('\n🎉 All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = testHolidayAPI;
