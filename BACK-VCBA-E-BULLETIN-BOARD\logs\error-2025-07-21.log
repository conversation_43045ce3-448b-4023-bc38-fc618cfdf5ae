{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:28'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:28'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:30:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:30:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:30:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:30:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:31:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:31:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:31:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:31:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:32:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:32:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:32:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:32:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:33:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:33:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:34:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:34:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:35:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:35:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:36:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:36:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:37:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:37:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:38:38'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:38:38'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:21'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:21'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/calendar/71/attachments',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:37'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:54'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:54'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:40:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:40:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:40:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:40:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:41:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:41:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:41:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:41:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:42:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:42:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:42:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:42:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:20'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:20'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:20'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:20'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/sync-multiple not found',
  stack: 'Error: Route /api/holidays/sync-multiple not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'POST',
    url: '/api/holidays/sync-multiple',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:09'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:09'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:09'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:09'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:13'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:13'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:13'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:13'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:16'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:16'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:16'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:16'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:16'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:16'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:16'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:16'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:17'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:17'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:17'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:17'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:18'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:18'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:18'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:18'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:19'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:19'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:19'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:19'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:19'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:19'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:38'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:38'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:38'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:22:38'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:18'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:18'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:18'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:18'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:24:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:25:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:25:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:25:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:25:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:28'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:28'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:28'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:28'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:32'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:32'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:32'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:32'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:37'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:37'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:37'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:29:37'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 5000,
  level: 'error',
  message: 'Server error: listen EADDRINUSE: address already in use :::5000',
  stack: 'Error: listen EADDRINUSE: address already in use :::5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n' +
    '    at listenInCluster (node:net:1965:12)\n' +
    '    at Server.listen (node:net:2067:7)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js:270:8)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:173:12)\n' +
    '    at node:internal/main/run_main_module:28:49',
  timestamp: '2025-07-21 09:30:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:31:03'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:31:03'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:31:03'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:31:03'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:31:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:31:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/2025 not found',
  stack: 'Error: Route /api/holidays/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:31:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/status/2025 not found',
  stack: 'Error: Route /api/holidays/status/2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/status/2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 09:31:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 5000,
  level: 'error',
  message: 'Server error: listen EADDRINUSE: address already in use :::5000',
  stack: 'Error: listen EADDRINUSE: address already in use :::5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n' +
    '    at listenInCluster (node:net:1965:12)\n' +
    '    at Server.listen (node:net:2067:7)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js:272:8)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:173:12)\n' +
    '    at node:internal/main/run_main_module:28:49',
  timestamp: '2025-07-21 09:50:52'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 5000,
  level: 'error',
  message: 'Server error: listen EADDRINUSE: address already in use :::5000',
  stack: 'Error: listen EADDRINUSE: address already in use :::5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n' +
    '    at listenInCluster (node:net:1965:12)\n' +
    '    at Server.listen (node:net:2067:7)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js:270:8)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:173:12)\n' +
    '    at node:internal/main/run_main_module:28:49',
  timestamp: '2025-07-21 11:02:46'
}
