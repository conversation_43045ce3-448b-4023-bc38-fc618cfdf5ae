const { asyncHandler } = require('../middleware/errorHandler');
const HolidayService = require('../services/HolidayService');
const logger = require('../utils/logger');

/**
 * Holiday Controller for managing public holidays
 * Handles holiday synchronization and management operations
 */
class HolidayController {
  constructor() {
    this.holidayService = new HolidayService();
  }

  /**
   * Sync holidays for a specific year
   * POST /api/holidays/sync
   */
  syncHolidays = asyncHandler(async (req, res) => {
    const { year, force_update = false } = req.body;

    // Validate year
    if (!year) {
      return res.status(400).json({
        success: false,
        message: 'Year is required'
      });
    }

    const yearNum = parseInt(year);
    const currentYear = new Date().getFullYear();
    
    if (yearNum < currentYear || yearNum > currentYear + 5) {
      return res.status(400).json({
        success: false,
        message: 'Year must be between current year and 5 years in the future'
      });
    }

    try {
      logger.info(`Admin ${req.user.id} initiated holiday sync for year ${yearNum}`);
      
      const results = await this.holidayService.syncHolidaysForYear(yearNum, force_update);
      
      res.status(200).json({
        success: true,
        message: `Holiday sync completed for year ${yearNum}`,
        data: results
      });
      
    } catch (error) {
      logger.error(`Holiday sync failed: ${error.message}`);
      res.status(500).json({
        success: false,
        message: 'Failed to sync holidays',
        error: error.message
      });
    }
  });

  /**
   * Get holiday sync status
   * GET /api/holidays/sync-status
   */
  getSyncStatus = asyncHandler(async (req, res) => {
    try {
      const status = await this.holidayService.getHolidaySyncStatus();
      
      res.status(200).json({
        success: true,
        message: 'Holiday sync status retrieved successfully',
        data: status
      });
      
    } catch (error) {
      logger.error(`Failed to get sync status: ${error.message}`);
      res.status(500).json({
        success: false,
        message: 'Failed to get sync status',
        error: error.message
      });
    }
  });

  /**
   * Get auto-generated holidays for a specific year
   * GET /api/holidays/:year
   */
  getHolidaysByYear = asyncHandler(async (req, res) => {
    const { year } = req.params;
    
    if (!year) {
      return res.status(400).json({
        success: false,
        message: 'Year is required'
      });
    }

    const yearNum = parseInt(year);
    if (isNaN(yearNum)) {
      return res.status(400).json({
        success: false,
        message: 'Year must be a valid number'
      });
    }

    try {
      const holidays = await this.holidayService.getAutoGeneratedHolidays(yearNum);
      
      res.status(200).json({
        success: true,
        message: `Holidays retrieved for year ${yearNum}`,
        data: {
          year: yearNum,
          holidays,
          total: holidays.length
        }
      });
      
    } catch (error) {
      logger.error(`Failed to get holidays for year ${yearNum}: ${error.message}`);
      res.status(500).json({
        success: false,
        message: `Failed to get holidays for year ${yearNum}`,
        error: error.message
      });
    }
  });

  /**
   * Delete auto-generated holidays for a specific year
   * DELETE /api/holidays/:year
   */
  deleteHolidaysByYear = asyncHandler(async (req, res) => {
    const { year } = req.params;
    
    if (!year) {
      return res.status(400).json({
        success: false,
        message: 'Year is required'
      });
    }

    const yearNum = parseInt(year);
    if (isNaN(yearNum)) {
      return res.status(400).json({
        success: false,
        message: 'Year must be a valid number'
      });
    }

    try {
      logger.info(`Admin ${req.user.id} deleting auto-generated holidays for year ${yearNum}`);
      
      const deletedCount = await this.holidayService.deleteAutoGeneratedHolidays(yearNum);
      
      res.status(200).json({
        success: true,
        message: `Deleted ${deletedCount} auto-generated holidays for year ${yearNum}`,
        data: {
          year: yearNum,
          deletedCount
        }
      });
      
    } catch (error) {
      logger.error(`Failed to delete holidays for year ${yearNum}: ${error.message}`);
      res.status(500).json({
        success: false,
        message: `Failed to delete holidays for year ${yearNum}`,
        error: error.message
      });
    }
  });

  /**
   * Test holiday API connectivity
   * GET /api/holidays/test-api
   */
  testAPI = asyncHandler(async (req, res) => {
    try {
      const currentYear = new Date().getFullYear();
      
      // Test fetching a small sample
      const phHolidays = await this.holidayService.fetchHolidaysFromAPI('PH', currentYear);
      const usHolidays = await this.holidayService.fetchHolidaysFromAPI('US', currentYear);
      
      // Count filtered holidays
      let phFiltered = 0;
      let intFiltered = 0;
      
      phHolidays.forEach(holiday => {
        const inclusion = this.holidayService.isHolidayIncluded(holiday.name, 'PH');
        if (inclusion.isIncluded) phFiltered++;
      });
      
      usHolidays.forEach(holiday => {
        const inclusion = this.holidayService.isHolidayIncluded(holiday.name, 'US');
        if (inclusion.isIncluded && inclusion.isGlobal) intFiltered++;
      });
      
      res.status(200).json({
        success: true,
        message: 'Holiday API test completed successfully',
        data: {
          apiStatus: 'operational',
          year: currentYear,
          philippineHolidays: {
            total: phHolidays.length,
            filtered: phFiltered
          },
          internationalHolidays: {
            total: usHolidays.length,
            filtered: intFiltered
          },
          totalRelevantHolidays: phFiltered + intFiltered
        }
      });
      
    } catch (error) {
      logger.error(`Holiday API test failed: ${error.message}`);
      res.status(500).json({
        success: false,
        message: 'Holiday API test failed',
        error: error.message,
        data: {
          apiStatus: 'error'
        }
      });
    }
  });

  /**
   * Get holiday configuration and supported countries
   * GET /api/holidays/config
   */
  getConfig = asyncHandler(async (req, res) => {
    try {
      const config = {
        supportedCountries: [
          { code: 'PH', name: 'Philippines', type: 'local' },
          { code: 'US', name: 'United States', type: 'international' }
        ],
        globalHolidays: this.holidayService.globalHolidays,
        philippineHolidays: this.holidayService.philippineHolidays,
        apiSource: 'nager.date',
        syncSettings: {
          autoSync: false, // Will be configurable later
          syncYearsAhead: 2,
          lastSync: null // Will be retrieved from database
        }
      };
      
      res.status(200).json({
        success: true,
        message: 'Holiday configuration retrieved successfully',
        data: config
      });
      
    } catch (error) {
      logger.error(`Failed to get holiday config: ${error.message}`);
      res.status(500).json({
        success: false,
        message: 'Failed to get holiday configuration',
        error: error.message
      });
    }
  });
}

module.exports = new HolidayController();
