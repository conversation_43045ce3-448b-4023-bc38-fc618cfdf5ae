const HolidayService = require('../src/services/HolidayService');
const calendarModel = require('../src/models/CalendarModel');
const http = require('http');

/**
 * Comprehensive Holiday System Test
 * Tests the complete holiday functionality end-to-end
 */
async function testCompleteHolidaySystem() {
  console.log('🧪 Testing Complete Holiday System...\n');

  const holidayService = new HolidayService();
  const currentYear = new Date().getFullYear();
  let testResults = {
    apiIntegration: false,
    dataStorage: false,
    calendarIntegration: false,
    holidayFiltering: false,
    endpointAccess: false,
    dataIntegrity: false
  };

  try {
    // Test 1: API Integration
    console.log('1️⃣ Testing API Integration...');
    try {
      const phHolidays = await holidayService.fetchHolidaysFromAPI('PH', currentYear);
      const usHolidays = await holidayService.fetchHolidaysFromAPI('US', currentYear);
      
      if (phHolidays.length > 0 && usHolidays.length > 0) {
        console.log(`✅ API Integration: Fetched ${phHolidays.length} PH + ${usHolidays.length} US holidays`);
        testResults.apiIntegration = true;
      } else {
        console.log('❌ API Integration: No holidays fetched');
      }
    } catch (error) {
      console.log(`❌ API Integration: ${error.message}`);
    }

    // Test 2: Holiday Filtering
    console.log('\n2️⃣ Testing Holiday Filtering...');
    try {
      const testHolidays = [
        { name: 'Christmas Day', country: 'US' },
        { name: 'Independence Day', country: 'PH' },
        { name: 'Thanksgiving', country: 'US' }, // Should be excluded
        { name: 'Random Local Holiday', country: 'PH' } // Should be excluded
      ];

      let filteredCount = 0;
      testHolidays.forEach(holiday => {
        const inclusion = holidayService.isHolidayIncluded(holiday.name, holiday.country);
        if (inclusion.isIncluded) {
          filteredCount++;
          console.log(`   ✅ ${holiday.name} (${holiday.country}) - ${inclusion.holidayType}`);
        } else {
          console.log(`   ❌ ${holiday.name} (${holiday.country}) - excluded`);
        }
      });

      if (filteredCount === 2) { // Should include Christmas and Independence Day only
        console.log('✅ Holiday Filtering: Working correctly');
        testResults.holidayFiltering = true;
      } else {
        console.log(`❌ Holiday Filtering: Expected 2, got ${filteredCount}`);
      }
    } catch (error) {
      console.log(`❌ Holiday Filtering: ${error.message}`);
    }

    // Test 3: Data Storage and Sync
    console.log('\n3️⃣ Testing Data Storage and Sync...');
    try {
      const syncResults = await holidayService.syncHolidaysForYear(currentYear, false);
      
      if (syncResults.totalFiltered > 0) {
        console.log(`✅ Data Storage: Synced ${syncResults.totalCreated + syncResults.totalUpdated + syncResults.totalSkipped} holidays`);
        console.log(`   - Created: ${syncResults.totalCreated}`);
        console.log(`   - Updated: ${syncResults.totalUpdated}`);
        console.log(`   - Skipped: ${syncResults.totalSkipped}`);
        testResults.dataStorage = true;
      } else {
        console.log('❌ Data Storage: No holidays synced');
      }
    } catch (error) {
      console.log(`❌ Data Storage: ${error.message}`);
    }

    // Test 4: Calendar Integration
    console.log('\n4️⃣ Testing Calendar Integration...');
    try {
      const holidays = await holidayService.getAutoGeneratedHolidays(currentYear);
      
      if (holidays.length > 0) {
        console.log(`✅ Calendar Integration: Found ${holidays.length} holidays in calendar`);
        
        // Check if holidays have proper calendar fields
        const sampleHoliday = holidays[0];
        const hasRequiredFields = sampleHoliday.calendar_id && 
                                 sampleHoliday.is_holiday && 
                                 sampleHoliday.is_auto_generated &&
                                 sampleHoliday.category_id &&
                                 sampleHoliday.subcategory_id;
        
        if (hasRequiredFields) {
          console.log('   ✅ Holidays have proper calendar structure');
          testResults.calendarIntegration = true;
        } else {
          console.log('   ❌ Holidays missing required calendar fields');
        }
      } else {
        console.log('❌ Calendar Integration: No holidays found in calendar');
      }
    } catch (error) {
      console.log(`❌ Calendar Integration: ${error.message}`);
    }

    // Test 5: API Endpoint Access
    console.log('\n5️⃣ Testing API Endpoint Access...');
    try {
      // Test public endpoints
      const configResult = await makeHttpRequest('GET', 'http://localhost:5000/api/holidays/config');
      const testApiResult = await makeHttpRequest('GET', 'http://localhost:5000/api/holidays/test-api');
      
      if (configResult.success && testApiResult.success) {
        console.log('✅ API Endpoints: Public endpoints accessible');
        console.log(`   - Config: ${configResult.data.globalHolidays.length} global holidays configured`);
        console.log(`   - Test API: ${testApiResult.data.totalRelevantHolidays} relevant holidays available`);
        testResults.endpointAccess = true;
      } else {
        console.log('❌ API Endpoints: Public endpoints not accessible');
      }
    } catch (error) {
      console.log(`❌ API Endpoints: ${error.message}`);
    }

    // Test 6: Data Integrity
    console.log('\n6️⃣ Testing Data Integrity...');
    try {
      const holidays = await calendarModel.query(
        `SELECT 
           COUNT(*) as total,
           COUNT(CASE WHEN is_holiday = 1 THEN 1 END) as holiday_count,
           COUNT(CASE WHEN is_auto_generated = 1 THEN 1 END) as auto_generated_count,
           COUNT(CASE WHEN holiday_type = 'international' THEN 1 END) as international_count,
           COUNT(CASE WHEN holiday_type = 'local' THEN 1 END) as local_count
         FROM school_calendar 
         WHERE YEAR(event_date) = ? AND deleted_at IS NULL`,
        [currentYear]
      );

      const stats = holidays[0];
      console.log(`✅ Data Integrity: Database statistics for ${currentYear}`);
      console.log(`   - Total events: ${stats.total}`);
      console.log(`   - Holiday events: ${stats.holiday_count}`);
      console.log(`   - Auto-generated: ${stats.auto_generated_count}`);
      console.log(`   - International: ${stats.international_count}`);
      console.log(`   - Local: ${stats.local_count}`);

      if (stats.holiday_count > 0 && stats.auto_generated_count > 0) {
        testResults.dataIntegrity = true;
      }
    } catch (error) {
      console.log(`❌ Data Integrity: ${error.message}`);
    }

    // Test Summary
    console.log('\n📊 Test Summary:');
    console.log('================');
    
    const passedTests = Object.values(testResults).filter(result => result).length;
    const totalTests = Object.keys(testResults).length;
    
    Object.entries(testResults).forEach(([test, passed]) => {
      const icon = passed ? '✅' : '❌';
      console.log(`${icon} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
    });
    
    console.log(`\nOverall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('\n🎉 All tests passed! Holiday system is fully functional.');
      console.log('\n📝 System is ready for production use:');
      console.log('✅ Holidays are automatically fetched from Nager.Date API');
      console.log('✅ Only globally recognized and Philippine holidays are included');
      console.log('✅ Holidays are stored in the existing school_calendar table');
      console.log('✅ Holidays display properly in the calendar with special styling');
      console.log('✅ Admin can manage holidays through API endpoints');
      console.log('✅ System supports automatic scheduling (when enabled)');
    } else {
      console.log(`\n⚠️ ${totalTests - passedTests} tests failed. Please review the issues above.`);
    }

    return { passed: passedTests, total: totalTests, results: testResults };

  } catch (error) {
    console.error('❌ Complete system test failed:', error.message);
    throw error;
  }
}

/**
 * Make HTTP request helper
 */
function makeHttpRequest(method, url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve(parsed);
        } catch (e) {
          reject(new Error(`Failed to parse response: ${e.message}`));
        }
      });
    });

    req.on('error', (err) => {
      reject(new Error(`Request failed: ${err.message}`));
    });

    req.end();
  });
}

// Run the test
if (require.main === module) {
  testCompleteHolidaySystem()
    .then((results) => {
      if (results.passed === results.total) {
        console.log('\n🚀 Holiday system is ready for use!');
        process.exit(0);
      } else {
        console.log('\n🔧 Some issues need to be addressed.');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 System test failed:', error.message);
      process.exit(1);
    });
}

module.exports = testCompleteHolidaySystem;
