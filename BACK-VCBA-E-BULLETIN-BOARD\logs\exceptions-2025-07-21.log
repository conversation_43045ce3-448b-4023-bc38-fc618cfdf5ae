{
  error: Error: Cannot find module '../middleware/asyncHandler'
  Require stack:
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\controllers\HolidayController.js
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\routes\holidayRoutes.js
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\server.js
      at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)
      at Module._load (node:internal/modules/cjs/loader:1051:27)
      at Module.require (node:internal/modules/cjs/loader:1311:19)
      at require (node:internal/modules/helpers:179:18)
      at Object.<anonymous> (D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\controllers\HolidayController.js:1:22)
      at Module._compile (node:internal/modules/cjs/loader:1469:14)
      at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
      at Module.load (node:internal/modules/cjs/loader:1288:32)
      at Module._load (node:internal/modules/cjs/loader:1104:12)
      at Module.require (node:internal/modules/cjs/loader:1311:19) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module '../middleware/asyncHandler'\n" +
    'Require stack:\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js\n' +
    "Error: Cannot find module '../middleware/asyncHandler'\n" +
    'Require stack:\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js\n' +
    '    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1051:27)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)\n' +
    '    at require (node:internal/modules/helpers:179:18)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js:1:22)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)',
  stack: "Error: Cannot find module '../middleware/asyncHandler'\n" +
    'Require stack:\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js\n' +
    '    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1051:27)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)\n' +
    '    at require (node:internal/modules/helpers:179:18)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js:1:22)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)',
  exception: true,
  date: 'Mon Jul 21 2025 09:27:54 GMT+0800 (Philippine Standard Time)',
  process: {
    pid: 40668,
    uid: null,
    gid: null,
    cwd: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v20.18.3',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js'
    ],
    memoryUsage: {
      rss: 75141120,
      heapTotal: 55746560,
      heapUsed: 30778264,
      external: 2220162,
      arrayBuffers: 16623
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 197455.843 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._resolveFilename',
      line: 1225,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._load',
      line: 1051,
      method: '_load',
      native: false
    },
    {
      column: 19,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    },
    {
      column: 18,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 179,
      method: null,
      native: false
    },
    {
      column: 22,
      file: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js',
      function: null,
      line: 1,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1469,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._extensions..js',
      line: 1548,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1288,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._load',
      line: 1104,
      method: '_load',
      native: false
    },
    {
      column: 19,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    }
  ],
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 09:27:54'
}
{
  error: Error: Cannot find module 'express-async-handler'
  Require stack:
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\controllers\HolidayController.js
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\routes\holidayRoutes.js
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\server.js
      at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)
      at Module._load (node:internal/modules/cjs/loader:1051:27)
      at Module.require (node:internal/modules/cjs/loader:1311:19)
      at require (node:internal/modules/helpers:179:18)
      at Object.<anonymous> (D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\controllers\HolidayController.js:1:22)
      at Module._compile (node:internal/modules/cjs/loader:1469:14)
      at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
      at Module.load (node:internal/modules/cjs/loader:1288:32)
      at Module._load (node:internal/modules/cjs/loader:1104:12)
      at Module.require (node:internal/modules/cjs/loader:1311:19) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'express-async-handler'\n" +
    'Require stack:\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js\n' +
    "Error: Cannot find module 'express-async-handler'\n" +
    'Require stack:\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js\n' +
    '    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1051:27)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)\n' +
    '    at require (node:internal/modules/helpers:179:18)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js:1:22)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)',
  stack: "Error: Cannot find module 'express-async-handler'\n" +
    'Require stack:\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js\n' +
    '    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1051:27)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)\n' +
    '    at require (node:internal/modules/helpers:179:18)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js:1:22)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)',
  exception: true,
  date: 'Mon Jul 21 2025 09:49:05 GMT+0800 (Philippine Standard Time)',
  process: {
    pid: 20152,
    uid: null,
    gid: null,
    cwd: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v20.18.3',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js'
    ],
    memoryUsage: {
      rss: 73641984,
      heapTotal: 54968320,
      heapUsed: 29489640,
      external: 2220162,
      arrayBuffers: 16623
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 198727.062 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._resolveFilename',
      line: 1225,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._load',
      line: 1051,
      method: '_load',
      native: false
    },
    {
      column: 19,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    },
    {
      column: 18,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 179,
      method: null,
      native: false
    },
    {
      column: 22,
      file: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js',
      function: null,
      line: 1,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1469,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._extensions..js',
      line: 1548,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1288,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._load',
      line: 1104,
      method: '_load',
      native: false
    },
    {
      column: 19,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    }
  ],
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 09:49:05'
}
{
  error: node:internal/modules/cjs/loader:1228
    throw err;
    ^
  
  Error: Cannot find module 'express-async-handler'
  Require stack:
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\controllers\HolidayController.js
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\routes\holidayRoutes.js
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\server.js
  - D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\[eval]
      at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)
      at Module._load (node:internal/modules/cjs/loader:1051:27)
      at Module.require (node:internal/modules/cjs/loader:1311:19)
      at require (node:internal/modules/helpers:179:18)
      at Object.<anonymous> (D:\online-e-bulletin-reactjs\BACK-VCBA-E-BULLETIN-BOARD\src\controllers\HolidayController.js:1:22)
      at Module._compile (node:internal/modules/cjs/loader:1469:14)
      at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
      at Module.load (node:internal/modules/cjs/loader:1288:32)
      at Module._load (node:internal/modules/cjs/loader:1104:12)
      at Module.require (node:internal/modules/cjs/loader:1311:19) {
    code: 'MODULE_NOT_FOUND',
    requireStack: [
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js',
      'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\[eval]'
    ]
  },
  level: 'error',
  message: "uncaughtException: Cannot find module 'express-async-handler'\n" +
    'Require stack:\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\[eval]\n' +
    'node:internal/modules/cjs/loader:1228\n' +
    '  throw err;\n' +
    '  ^\n' +
    '\n' +
    "Error: Cannot find module 'express-async-handler'\n" +
    'Require stack:\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\[eval]\n' +
    '    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1051:27)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)\n' +
    '    at require (node:internal/modules/helpers:179:18)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js:1:22)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)',
  stack: 'node:internal/modules/cjs/loader:1228\n' +
    '  throw err;\n' +
    '  ^\n' +
    '\n' +
    "Error: Cannot find module 'express-async-handler'\n" +
    'Require stack:\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\routes\\holidayRoutes.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\server.js\n' +
    '- D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\[eval]\n' +
    '    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1051:27)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)\n' +
    '    at require (node:internal/modules/helpers:179:18)\n' +
    '    at Object.<anonymous> (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js:1:22)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1288:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1104:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1311:19)',
  exception: true,
  date: 'Mon Jul 21 2025 09:49:45 GMT+0800 (Philippine Standard Time)',
  process: {
    pid: 30752,
    uid: null,
    gid: null,
    cwd: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v20.18.3',
    argv: [ 'C:\\Program Files\\nodejs\\node.exe' ],
    memoryUsage: {
      rss: 73711616,
      heapTotal: 54706176,
      heapUsed: 29541472,
      external: 2280047,
      arrayBuffers: 16623
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 198767.062 },
  trace: [
    {
      column: 15,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._resolveFilename',
      line: 1225,
      method: '_resolveFilename',
      native: false
    },
    {
      column: 27,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._load',
      line: 1051,
      method: '_load',
      native: false
    },
    {
      column: 19,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    },
    {
      column: 18,
      file: 'node:internal/modules/helpers',
      function: 'require',
      line: 179,
      method: null,
      native: false
    },
    {
      column: 22,
      file: 'D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\controllers\\HolidayController.js',
      function: null,
      line: 1,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1469,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._extensions..js',
      line: 1548,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1288,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._load',
      line: 1104,
      method: '_load',
      native: false
    },
    {
      column: 19,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.require',
      line: 1311,
      method: 'require',
      native: false
    }
  ],
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 09:49:45'
}
