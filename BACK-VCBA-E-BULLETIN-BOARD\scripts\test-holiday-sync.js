const HolidayService = require('../src/services/HolidayService');

async function testHolidaySync() {
  try {
    console.log('🧪 Testing Holiday Sync Functionality...\n');
    
    const holidayService = new HolidayService();
    const currentYear = new Date().getFullYear();
    
    console.log('📊 Getting current sync status...');
    const initialStatus = await holidayService.getHolidaySyncStatus();
    console.log('Initial status:', JSON.stringify(initialStatus, null, 2));
    
    console.log('\n🔄 Testing holiday sync for current year...');
    const syncResults = await holidayService.syncHolidaysForYear(currentYear, false);
    
    console.log('\n📈 Sync Results:');
    console.log(`- Year: ${syncResults.year}`);
    console.log(`- Total fetched from APIs: ${syncResults.totalFetched}`);
    console.log(`- Total filtered (relevant): ${syncResults.totalFiltered}`);
    console.log(`- Created: ${syncResults.totalCreated}`);
    console.log(`- Updated: ${syncResults.totalUpdated}`);
    console.log(`- Skipped: ${syncResults.totalSkipped}`);
    console.log(`- Errors: ${syncResults.errors.length}`);
    
    if (syncResults.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      syncResults.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    console.log('\n📅 Synced holidays:');
    syncResults.holidays.forEach(holiday => {
      const icon = holiday.action === 'created' ? '✅' : 
                   holiday.action === 'updated' ? '🔄' : '⏭️';
      console.log(`  ${icon} ${holiday.name} (${holiday.date}) - ${holiday.type} [${holiday.action}]`);
    });
    
    console.log('\n📊 Getting updated sync status...');
    const finalStatus = await holidayService.getHolidaySyncStatus();
    console.log('Final status:', JSON.stringify(finalStatus, null, 2));
    
    console.log('\n🔍 Testing retrieval of auto-generated holidays...');
    const holidays = await holidayService.getAutoGeneratedHolidays(currentYear);
    console.log(`Found ${holidays.length} auto-generated holidays for ${currentYear}:`);
    
    holidays.forEach(holiday => {
      const type = holiday.is_global ? '🌍' : '🇵🇭';
      console.log(`  ${type} ${holiday.title} (${holiday.event_date}) - ${holiday.holiday_type}`);
    });
    
    console.log('\n✅ Holiday sync test completed successfully!');
    
    return {
      syncResults,
      finalStatus,
      holidayCount: holidays.length
    };
    
  } catch (error) {
    console.error('❌ Holiday sync test failed:', error.message);
    console.error('Stack trace:', error.stack);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testHolidaySync()
    .then((results) => {
      console.log('\n🎉 Holiday sync test completed successfully!');
      console.log(`Summary: ${results.syncResults.totalCreated} created, ${results.syncResults.totalUpdated} updated, ${results.holidayCount} total holidays`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Holiday sync test failed:', error.message);
      process.exit(1);
    });
}

module.exports = testHolidaySync;
