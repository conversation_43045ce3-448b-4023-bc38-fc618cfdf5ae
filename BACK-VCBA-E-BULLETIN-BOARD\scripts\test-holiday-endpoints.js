const http = require('http');

/**
 * Test Holiday API Endpoints
 * Tests all holiday management endpoints
 */
async function testHolidayEndpoints() {
  console.log('🧪 Testing Holiday API Endpoints...\n');

  const baseUrl = 'http://localhost:5000';
  const currentYear = new Date().getFullYear();

  // Test public endpoints first
  console.log('📋 Testing public endpoints...');
  
  try {
    // Test API configuration endpoint
    console.log('1. Testing GET /api/holidays/config');
    const configResult = await makeRequest('GET', `${baseUrl}/api/holidays/config`);
    console.log('✅ Config endpoint working');
    console.log(`   - Supported countries: ${configResult.data.supportedCountries.length}`);
    console.log(`   - Global holidays: ${configResult.data.globalHolidays.length}`);
    console.log(`   - Philippine holidays: ${configResult.data.philippineHolidays.length}`);

    // Test API connectivity
    console.log('\n2. Testing GET /api/holidays/test-api');
    const testResult = await makeRequest('GET', `${baseUrl}/api/holidays/test-api`);
    console.log('✅ API test endpoint working');
    console.log(`   - API Status: ${testResult.data.apiStatus}`);
    console.log(`   - Total relevant holidays: ${testResult.data.totalRelevantHolidays}`);

  } catch (error) {
    console.log('❌ Public endpoint test failed:', error.message);
  }

  // Note: Protected endpoints require authentication
  console.log('\n📋 Testing protected endpoints (requires admin auth)...');
  console.log('⚠️ Skipping protected endpoint tests - requires authentication');
  console.log('   Protected endpoints:');
  console.log('   - POST /api/holidays/sync');
  console.log('   - GET /api/holidays/sync-status');
  console.log('   - GET /api/holidays/:year');
  console.log('   - DELETE /api/holidays/:year');

  console.log('\n✅ Holiday endpoint tests completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Start the server: npm start');
  console.log('2. Login as admin in the frontend');
  console.log('3. Navigate to Holiday Management page');
  console.log('4. Test holiday sync functionality');
}

/**
 * Make HTTP request
 */
function makeRequest(method, url, data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Holiday-Test-Script/1.0'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsed);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${parsed.message || 'Unknown error'}`));
          }
        } catch (e) {
          reject(new Error(`Failed to parse response: ${e.message}`));
        }
      });
    });

    req.on('error', (err) => {
      reject(new Error(`Request failed: ${err.message}`));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Run the test
if (require.main === module) {
  testHolidayEndpoints()
    .then(() => {
      console.log('\n🎉 All tests completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = testHolidayEndpoints;
