const cron = require('cron');
const HolidayService = require('./HolidayService');
const logger = require('../utils/logger');

/**
 * Holiday Scheduler Service
 * Manages automatic holiday synchronization using cron jobs
 */
class HolidayScheduler {
  constructor() {
    this.holidayService = new HolidayService();
    this.jobs = new Map();
    this.isEnabled = process.env.HOLIDAY_AUTO_SYNC === 'true' || false;
    this.syncTime = process.env.HOLIDAY_SYNC_TIME || '0 2 1 1 *'; // Default: January 1st at 2:00 AM
  }

  /**
   * Start the holiday sync scheduler
   */
  start() {
    if (!this.isEnabled) {
      logger.info('Holiday auto-sync is disabled. Set HOLIDAY_AUTO_SYNC=true to enable.');
      return;
    }

    try {
      // Annual holiday sync job
      const annualSyncJob = new cron.CronJob(
        this.syncTime, // Run on January 1st at 2:00 AM
        this.performAnnualSync.bind(this),
        null, // onComplete
        false, // start immediately
        'Asia/Manila' // timezone
      );

      // Monthly check job (1st of each month at 3:00 AM)
      const monthlyCheckJob = new cron.CronJob(
        '0 3 1 * *', // 1st of every month at 3:00 AM
        this.performMonthlyCheck.bind(this),
        null,
        false,
        'Asia/Manila'
      );

      this.jobs.set('annualSync', annualSyncJob);
      this.jobs.set('monthlyCheck', monthlyCheckJob);

      // Start the jobs
      annualSyncJob.start();
      monthlyCheckJob.start();

      logger.info('Holiday scheduler started successfully');
      logger.info(`Annual sync scheduled for: ${this.syncTime}`);
      logger.info('Monthly check scheduled for: 1st of each month at 3:00 AM');

    } catch (error) {
      logger.error(`Failed to start holiday scheduler: ${error.message}`);
      throw error;
    }
  }

  /**
   * Stop the holiday sync scheduler
   */
  stop() {
    try {
      this.jobs.forEach((job, name) => {
        job.stop();
        logger.info(`Stopped holiday scheduler job: ${name}`);
      });
      this.jobs.clear();
      logger.info('Holiday scheduler stopped successfully');
    } catch (error) {
      logger.error(`Failed to stop holiday scheduler: ${error.message}`);
    }
  }

  /**
   * Perform annual holiday sync
   * Syncs holidays for current year and next year
   */
  async performAnnualSync() {
    try {
      logger.info('Starting scheduled annual holiday sync...');
      
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;
      
      const results = {
        timestamp: new Date().toISOString(),
        currentYear: null,
        nextYear: null,
        totalCreated: 0,
        totalUpdated: 0,
        errors: []
      };

      // Sync current year (force update to refresh any changes)
      try {
        logger.info(`Syncing holidays for current year: ${currentYear}`);
        const currentYearResult = await this.holidayService.syncHolidaysForYear(currentYear, true);
        results.currentYear = currentYearResult;
        results.totalCreated += currentYearResult.totalCreated;
        results.totalUpdated += currentYearResult.totalUpdated;
        logger.info(`Current year sync completed: ${currentYearResult.totalCreated} created, ${currentYearResult.totalUpdated} updated`);
      } catch (error) {
        const errorMsg = `Failed to sync current year ${currentYear}: ${error.message}`;
        logger.error(errorMsg);
        results.errors.push(errorMsg);
      }

      // Sync next year (new holidays)
      try {
        logger.info(`Syncing holidays for next year: ${nextYear}`);
        const nextYearResult = await this.holidayService.syncHolidaysForYear(nextYear, false);
        results.nextYear = nextYearResult;
        results.totalCreated += nextYearResult.totalCreated;
        results.totalUpdated += nextYearResult.totalUpdated;
        logger.info(`Next year sync completed: ${nextYearResult.totalCreated} created, ${nextYearResult.totalUpdated} updated`);
      } catch (error) {
        const errorMsg = `Failed to sync next year ${nextYear}: ${error.message}`;
        logger.error(errorMsg);
        results.errors.push(errorMsg);
      }

      // Log summary
      if (results.errors.length === 0) {
        logger.info(`Annual holiday sync completed successfully. Total: ${results.totalCreated} created, ${results.totalUpdated} updated`);
      } else {
        logger.warn(`Annual holiday sync completed with ${results.errors.length} errors. Total: ${results.totalCreated} created, ${results.totalUpdated} updated`);
      }

      return results;

    } catch (error) {
      logger.error(`Annual holiday sync failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Perform monthly check
   * Checks if holidays are up to date and logs status
   */
  async performMonthlyCheck() {
    try {
      logger.info('Starting scheduled monthly holiday check...');
      
      const status = await this.holidayService.getHolidaySyncStatus();
      const currentYear = new Date().getFullYear();
      const nextYear = currentYear + 1;

      // Check if current year has holidays
      if (status.currentYear.total_holidays === 0) {
        logger.warn(`No holidays found for current year ${currentYear}. Consider running manual sync.`);
      } else {
        logger.info(`Current year ${currentYear}: ${status.currentYear.total_holidays} holidays (${status.currentYear.international_count} international, ${status.currentYear.local_count} local)`);
      }

      // Check if next year has holidays (after July, we should have next year's holidays)
      const currentMonth = new Date().getMonth() + 1; // 1-based month
      if (currentMonth >= 7 && status.nextYear.total_holidays === 0) {
        logger.warn(`No holidays found for next year ${nextYear}. Consider running manual sync.`);
      } else if (status.nextYear.total_holidays > 0) {
        logger.info(`Next year ${nextYear}: ${status.nextYear.total_holidays} holidays (${status.nextYear.international_count} international, ${status.nextYear.local_count} local)`);
      }

      // Check last sync time
      if (status.currentYear.last_sync) {
        const lastSync = new Date(status.currentYear.last_sync);
        const daysSinceSync = Math.floor((Date.now() - lastSync.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysSinceSync > 365) {
          logger.warn(`Last holiday sync was ${daysSinceSync} days ago. Consider running manual sync.`);
        } else {
          logger.info(`Last holiday sync was ${daysSinceSync} days ago.`);
        }
      } else {
        logger.warn('No previous holiday sync found. Consider running manual sync.');
      }

      logger.info('Monthly holiday check completed');
      return status;

    } catch (error) {
      logger.error(`Monthly holiday check failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Manually trigger annual sync
   */
  async triggerAnnualSync() {
    logger.info('Manual annual holiday sync triggered');
    return await this.performAnnualSync();
  }

  /**
   * Manually trigger monthly check
   */
  async triggerMonthlyCheck() {
    logger.info('Manual monthly holiday check triggered');
    return await this.performMonthlyCheck();
  }

  /**
   * Get scheduler status
   */
  getStatus() {
    const jobStatuses = {};
    this.jobs.forEach((job, name) => {
      jobStatuses[name] = {
        running: job.running,
        lastDate: job.lastDate(),
        nextDate: job.nextDate()
      };
    });

    return {
      enabled: this.isEnabled,
      syncTime: this.syncTime,
      jobs: jobStatuses,
      totalJobs: this.jobs.size
    };
  }

  /**
   * Update scheduler configuration
   */
  updateConfig(config) {
    if (config.enabled !== undefined) {
      this.isEnabled = config.enabled;
    }
    
    if (config.syncTime) {
      this.syncTime = config.syncTime;
      // Restart scheduler with new time if it's running
      if (this.jobs.size > 0) {
        this.stop();
        this.start();
      }
    }

    logger.info(`Holiday scheduler configuration updated: enabled=${this.isEnabled}, syncTime=${this.syncTime}`);
  }
}

module.exports = new HolidayScheduler();
